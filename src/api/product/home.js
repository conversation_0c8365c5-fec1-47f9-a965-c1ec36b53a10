import {
    getRequest,
    postRequestBody3,
    getRequestByValue
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataCount: baseURL + 'view/product/overview',
    projectList: baseURL + 'view/schemeModule/combine/page',
    tradeList: baseURL + 'view/solution/category/list',
    providerList: baseURL + 'backend/system/org/list?level=2',
    labelList: baseURL + 'backend/system/label/list',
    newLabel: baseURL + 'backend/product/label/page',
    productList: baseURL + 'view/product/chart',
    allProductList: baseURL + 'backend/product/list',
    marketSelect: baseURL + 'backend/product/label/tree',
    newAllList: baseURL + 'backend/ykProduct/list',
    newProductDetail:baseURL + 'backend/ykProduct/',
}

// 获取产品列表
export const getAllProductList = (data) => getRequest(url.allProductList, data)
// 获取新商客产品
export const getProductAll = (data) => getRequest(url.newAllList, data)
// 获取新商客产品详情
export const getNewProductDetail = (data) => getRequestByValue(url.newProductDetail, data)

// 获取数据总览
export const getCount = (data) => getRequest(url.dataCount, data)

// 获取方案列表
export const getProjectList = (data) => getRequest(url.projectList, data)
export const getSchemeList = (data) => getRequest(url.projectList, data)

// 获取类目列表
export const getTradeList = (data) => getRequest(url.tradeList, data)
// 获取类目列表
export const getProviderList = (data) => getRequest(url.providerList, data)
// 获取类目列表
export const getLabelList = (data) => getRequest(url.labelList, data)

// 获取标签列表
export const getNewLabel = (data) => getRequest(url.newLabel, data)
// 获取产品列表
export const getProductList = (data) => getRequest(url.productList, data)
// 获取市场联动列表
export const marketSelect = (data) => getRequest(url.marketSelect, data)