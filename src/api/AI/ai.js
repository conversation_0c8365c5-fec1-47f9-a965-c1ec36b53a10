import {
    getRequest,
    postRequestBody,
    AIBlobRequest,
    deleteRequestBody,
    postRequestBody3,
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    getReplay: baseURL + 'view/ai/reply',
    modelList: baseURL + 'view/schemeModule/combine/page',
    getAssociate: baseURL + 'view/ai/modelSession',
    addGroup: baseURL + 'view/shoppingCart/add',
    AISearch: baseURL + 'view/ai/modelViewList',
    AIvoice: baseURL + 'view/ai/modelVoice',
    getNewAssociate: baseURL + 'view/ai/changAiList',
    productMerge: baseURL + 'view/product/package/mergeFile',
    getStep: baseURL + 'view/ai/aiIntention',
    clearAll: baseURL + "view/product/package/clear",
    hotSearch: baseURL + "view/ai/hotspotKeywords",
    semantics: baseURL + "view/ai/xyhjIntention",
    separateAIList: baseURL + "view/ai/addViewList",
    moreChat: baseURL + "view/multiChat/dialogue",
    retrieveList: baseURL + "view/multiChat/retrieveList",
    clearSession: baseURL + "view/multiChat/clearSession",
    elementExpand: baseURL + "view/multiChat/solutionExpandData",
    AIChatHistory: baseURL + "view/multiChat/dialogueTitle",
    deleteAISearchHistory: baseURL + "view/multiChat/dialogueData",
    getChatContent: baseURL + "view/multiChat/dialogueList",
    storeChatResult: baseURL + "view/multiChat/dialogueEnd",
    clearAllChat: baseURL + "view/multiChat/dropDialogue",
    getSummaryData: baseURL + "view/ai/overviewData",
    AILackDownload: baseURL + "view/ai/lackDownload",
    historyList: `${baseURL}backend/workOrder/audit/history`,
    updateChatResult: baseURL + "view/multiChat/dialogueUpdate",
    getChatJSON: baseURL + "view/streamChat/pptJson",
    getAIPPT: baseURL + "view/streamChat/pptMerge",
    elementPPTExpand: baseURL + "view/streamChat/pptElementThinking",
    promptWords: baseURL + "view/streamChat/guideMessage",
    fullRetrieveList: baseURL + "view/ai/concurrentAll",
    newGo: baseURL + "view/combine/draft/ai/go",
    startVideoBusiness: baseURL + "view/videoDigital/startVideoBusiness",
    elementRetrial: baseURL + "view/streamChat/elementRetrial",
}

// 获取方案总览
export const getReplay = (data) => getRequest(url.getReplay, data)

export const getModelList = (data) => getRequest(url.modelList, data)

export const getAssociate = (data) => getRequest(url.getAssociate, data)

export const addGroup = (data) => postRequestBody(url.addGroup, data)
export const productMerge = (data) => postRequestBody(url.productMerge, data)

export const AISearch = (data) => getRequest(url.AISearch, data)
export const getStep = (data) => getRequest(url.getStep, data)

export const AIvoice = (data) => AIBlobRequest(url.AIvoice, data)

export const getNewAssociate = (data) => getRequest(url.getNewAssociate, data)

export const clearAll = (data) => deleteRequestBody(url.clearAll, data)

export const getHotSearch = (data) => getRequest(url.hotSearch, data)

export const getSemantics = (data) => getRequest(url.semantics, data)

export const getSeparateAIList = (data) => postRequestBody(url.separateAIList, data)

// 多轮对话
export const getMoreChat = (data) => postRequestBody(url.moreChat, data)

// 单招回
export const getRetrieveList = (data) => postRequestBody(url.retrieveList, data)

// 清除SessionID
export const clearSession = (data) => getRequest(url.clearSession, data)

// 要素思考
export const getElementExpand = (data) => postRequestBody(url.elementExpand, data)

// 多轮对话历史
export const getAISearchHistoryList = (data) => getRequest(url.AIChatHistory, data)

// 删除历史记录
export const deleteAISearchHistory = (data) => deleteRequestBody(url.deleteAISearchHistory, data)

// 获取对话内容
export const getChatContent = (data) => getRequest(url.getChatContent, data)

// 存储对话结果
export const storeChatResult = (data) => postRequestBody(url.storeChatResult, data)

//清除所有聊天记录
export const clearAllChat = (data) => deleteRequestBody(url.clearAllChat, data)

// 获取汇总数据
export const getSummaryData = (data) => getRequest(url.getSummaryData, data)

// 下载AI缺失的东西
export const getAILackDownload = (data) => getRequest(url.AILackDownload, data)
// 查询历史记录
export const getHistoryList = (data) => getRequest(url.historyList, data)

// 更新对话结果
export const updateChatResult = (data) => postRequestBody(url.updateChatResult, data)

// 获取对话JSON
export const getChatJSON = (data) => getRequest(url.getChatJSON, data)

// 获取AI3.0的PPT
export const getAIPPT = (data) => postRequestBody(url.getAIPPT, data)

// PPT的要素思考
export const getPPTElementExpand = (data) => postRequestBody(url.elementPPTExpand, data)

// 获取提示话术
export const getPromptWords = (data) => getRequest(url.promptWords, data)

// 获取全量检索数据
export const getFullRetrieveList = (data) => getRequest(url.fullRetrieveList, data)

// 新的AIgo接口
export const newGo = (data) => postRequestBody3(url.newGo, data)

// 数字人视频生成请求
export const getStartVideoBusiness = (data) => postRequestBody(url.startVideoBusiness, data)

// 要素召回的并发
export const getElementRetrial = (data) => postRequestBody(url.elementRetrial, data)

