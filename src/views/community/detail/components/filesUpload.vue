<template>
  <div class="file-upload-container">
    <div v-if="!isMaxFilesUploaded" class="upload-header">
      <a-upload :customRequest="uploadFile" :max-count="maxCount" :accept="acceptTypes" :show-upload-list="false">
        <a-button class="upload-button" :loading="uploading">上传附件</a-button>
      </a-upload>
      <div class="upload-note">
        注：视频限制{{ maxVideoSize / (1024 * 1024) }}M 以内
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="Object.keys(groupedFiles).length > 0" class="grouped-file-list">
      <div class="file-category" v-for="(files, fileType) in groupedFiles" :key="fileType">
        <h4 class="file-type-title">{{ getFileTypeDisplayName(fileType) }}</h4>
        <div class="file-grid">
          <div v-for="(file, index) in files" :key="file.fileName + index" class="file-item">
            <div class="file-icon" :class="'icon-' + fileType">{{ fileType.toUpperCase() }}</div>

            <div class="file-info">
              <div class="file-name">{{ file.fileName }}</div>
              <div class="file-size">{{ formatFileSize(file.fileSize) }}</div>
            </div>

            <a-tooltip title="删除文件">
              <a-button type="text" danger @click.stop="handleRemove(file)" class="delete-btn" size="small">
                <delete-outlined style="font-size: 14px;" />
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { reactive } from 'vue';

export default {
  name: 'FileUpload',
  components: {
    DeleteOutlined
  },
  props: {
    // 文件列表
    modelValue: {
      type: Array,
      default: () => []
    },
    // 最大文件数量
    maxCount: {
      type: Number,
      default: 10
    },
    // 接受的文件类型
    acceptTypes: {
      type: String,
      default: ".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
    },
    // 视频文件最大大小（字节）
    maxVideoSize: {
      type: Number,
      default: 20 * 1024 * 1024 // 20MB
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const uploading = ref(false);
    const internalFileList = reactive([...props.modelValue]);

    // 计算属性
    const isMaxFilesUploaded = computed(() => internalFileList.length >= props.maxCount);

    const groupedFiles = computed(() => {
      return internalFileList.reduce((groups, file) => {
        const fileType = getFileType(file.fileName);
        if (!groups[fileType]) groups[fileType] = [];
        groups[fileType].push(file);
        return groups;
      }, {});
    });

    // 方法
    const getFileType = (fileName) => {
      if (!fileName) return "unknown";
      const ext = fileName.toLowerCase().split(".").pop();
      if (["doc", "docx"].includes(ext)) return "word";
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["jpg", "jpeg", "png"].includes(ext)) return "image";
      if (["pdf"].includes(ext)) return "pdf";
      if (["mp4", "mov", "avi"].includes(ext)) return "video";
      return "unknown";
    };
    const getFileTypeDisplayName = (type) => {
      const mapping = {
        word: "Word 文件",
        excel: "Excel 文件",
        image: "图片文件",
        pdf: "PDF 文件",
        video: "视频文件",
        unknown: "其他文件",
      };
      return mapping[type] || "未知";
    };

    const formatFileSize = (size) => {
      return size ? `${(size / 1024).toFixed(1)} KB` : "未知大小";
    };

    // 上传文件
    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");

      if (isVideo && file.size > props.maxVideoSize) {
        message.error(`视频文件大小不能超过 ${props.maxVideoSize / (1024 * 1024)}MB`);
        info.onError();
        return;
      }

      if (internalFileList.length >= props.maxCount) {
        message.error(`最多只能上传 ${props.maxCount} 个文件`);
        info.onError();
        return;
      }

      uploading.value = true;

      try {
        const formData = new FormData();
        formData.append("file", file);

        const result = await uploadFileList(formData);

        if (result.code === 200) {
          internalFileList.push({
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
            fileSize: file.size,
          });

          // 通知父组件更新 modelValue
          emit("update:modelValue", internalFileList);
          message.success("上传成功");
          info.onSuccess();
        } else {
          message.error("上传失败");
          info.onError();
        }
      } catch (error) {
        console.error("上传出错:", error);
        message.error("上传出错");
        info.onError();
      } finally {
        uploading.value = false;
      }
    };
    const handleRemove = (fileToRemove) => {
      const index = internalFileList.indexOf(fileToRemove);
      if (index >= 0) {
        internalFileList.splice(index, 1);
        emit("update:modelValue", internalFileList);
        message.success("文件已删除");
      }
    };


    return {
      uploading,
      internalFileList,
      isMaxFilesUploaded,
      groupedFiles,
      getFileType,
      getFileTypeDisplayName,
      formatFileSize,
      uploadFile,
      handleRemove

    };
  },
};
</script>

<style lang="scss" scoped>
.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.upload-button {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
}

.upload-note {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.25);
  margin-left: 14px;
}

.grouped-file-list {
  margin-top: 16px;
}

.file-category {
  margin-bottom: 20px;
}

.file-type-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  background: #f8f9fa;
  border-radius: 6px;
  transition: box-shadow 0.3s ease;
}

.file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  border-radius: 6px;
  margin-right: 12px;
  text-transform: uppercase;
}

.icon-pdf {
  background: #e74c3c;
}

.icon-word {
  background: #2980b9;
}

.icon-excel {
  background: #27ae60;
}

.icon-image {
  background: #8e44ad;
}

.icon-video {
  background: #e67e22;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #999;
}
</style>