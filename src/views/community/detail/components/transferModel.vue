<template>
    <el-config-provider :locale="zhCn">
        <div class="modelContent">
            <div class="title">
                <img src="@/assets/images/community/arrowRight.png" width="14px" height="8px" style="margin-right: 10px"
                    alt="" />
                <img src="@/assets/images/community/word.png" width="72px" height="18px" alt="转派任务" />
            </div>

            <!-- 表单内容 -->
            <div class="margin_t_20">
                <a-form ref="formRef" :model="formState" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }"
                    label-align="right">
                    <a-form-item label="办理部门">
                        <a-input v-model:value="formState.orgNamePath" disabled />
                    </a-form-item>

                    <a-form-item label="办理人" name="selectedUser" :rules="[{ required: true, message: '请选择办理人!' }]">
                        <a-select v-model:value="formState.selectedUser" placeholder="请选择办理人" :options="userOptions" />
                    </a-form-item>
                </a-form>
            </div>

            <!-- 操作按钮 -->
            <div class="flex just-center align-center margin_t_32">
                <a-button @click="closeModal" type="primary" style="background: rgba(1, 61, 253, 0.1); 
                 border-radius: 4px;
                 font-weight: 500;
                 border: none;
                 margin-left: 16px;
                 color: #0c70eb;">
                    取消
                </a-button>
                <a-button :loading="loading" type="primary" @click="submit" style="background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
                 border-radius: 4px;
                 font-weight: 500;
                 border: none;
                 margin-left: 16px;">
                    确认
                </a-button>
            </div>
        </div>
    </el-config-provider>
</template>

<script>
import { defineComponent, reactive, ref, onMounted } from "vue";
// import { getUserList } from "@/api/community/index.js";
import { getUserList } from "@/api/system/user.js";

import zhCn from "element-plus/es/locale/lang/zh-cn";
import { message } from "ant-design-vue";

export default defineComponent({
    emits: ["modal-close", "refresh"],
    setup(_, { emit }) {
        // 表单引用
        const formRef = ref();

        // 用户登录信息
        const userInfo = JSON.parse(window.localStorage.getItem("userInfo") || "{}");

        const orgId = userInfo.orgId; // 获取登录用户部门ID
        const orgNamePath = userInfo.orgNamePath || "";

        // 表单状态
        const formState = reactive({
            orgNamePath: orgNamePath,
            selectedUser: null,
        });

        // 办理人选项
        const userOptions = ref([]);

        const loading = ref(false);

        // 获取同部门成员
        const loadOrgUsers = async () => {
            if (!orgId) {
                console.warn("部门ID不存在，无法获取部门成员");
                return;
            }
            const params = {
                pageNo: 1,
                pageSize: 100,
                orgId: orgId,
            };

            try {
                const res = await getUserList(params);
                if (res.code === 200 && res.data) {
                    const currentUserId = userInfo.id; // 获取当前用户ID
                    const filteredUsers = res.data.rows.filter(user => user.id !== currentUserId);

                    userOptions.value = filteredUsers.map((user) => ({
                        label: user.realName,
                        value: user.id,
                    }));
                } else {
                    console.error("获取部门成员失败：", res.message || "未知错误");
                }
            } catch (error) {
                console.error("获取部门成员接口调用失败：", error);
            }
        };

        const validateForm = async () => {
            try {
                await formRef.value?.validate();
                return true;
            } catch (error) {
                console.log("表单验证失败:", error);
                return false;
            }
        };

        // 提交表单
        const submit = async () => {
            const isValid = await validateForm();
            if (!isValid) {
                return;
            }

            loading.value = true;

            try {
                // 模拟请求提交后端
                await new Promise((resolve) => setTimeout(resolve, 1000));

                console.log("提交成功，办理人为:", formState.selectedUser); // 输出办理人ID
                message.success("转派成功！");

                emit("refresh"); // 通知刷新
                closeModal(); // 关闭弹窗
            } catch (error) {
                console.error("提交失败:", error);
                message.error("提交失败，请重试！");
            } finally {
                loading.value = false;
            }
        };

        const closeModal = () => {
            formRef.value?.resetFields();
            formState.selectedUser = null;
            emit("modal-close");
        };

        // 初始化加载
        onMounted(() => {
            loadOrgUsers();
        });

        return {
            zhCn,
            formRef,
            formState,
            userOptions,
            loading,
            submit,
            closeModal,
        };
    },
});
</script>

<style lang="scss" scoped>
.modelContent {
    margin: 0 16px;
}

.title {
    margin-top: -8px;
    display: flex;
    align-items: center;
}

.flex {
    display: flex;
}

.just-center {
    justify-content: center;
}

.align-center {
    align-items: center;
}

.margin_t_20 {
    margin-top: 20px;
}

.margin_t_32 {
    margin-top: 32px;
}

.margin_r_16 {
    margin-right: 16px;
}

.margin_t_4 {
    margin-top: 4px;
}

// 自定义表单样式
:deep(.ant-form-item) {
    margin-bottom: 20px;
}

:deep(.ant-form-item-label) {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-form-item-required::before) {
    color: red;
}

// 保持标签间距效果
:deep(.ant-form-item-label > label) {
    letter-spacing: 1px;
}
</style>