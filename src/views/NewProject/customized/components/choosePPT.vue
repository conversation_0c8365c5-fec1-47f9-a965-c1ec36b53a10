<template>
    <div class="choosePPT">
        <div class="choosePPT-title margin_b_10">PPT选择</div>
        <div class="choosePPT-content maxWidth flex">
            <!-- 左侧目录 -->
            <div class="choosePPT-catalogue maxHeight">
                <div v-for="(item, index) in catalogueList" :key="index">
                    <!-- 一级标题 -->
                    <div class="catalogue-first flex just-sb align-center" :class="{ 'active': item.isActive }">
                        <div @click="selectFirstLevel(index)">
                            {{ item.name }}
                        </div>
                        <div v-if="item.children && item.children.length > 0" class="arrow pointer"
                            :class="{ 'arrow-rotate': item.isExpanded }" @click.stop="toggleExpand(index)">
                            <img src="../../../../assets/images/AI/openHistory.png" alt="">
                        </div>
                    </div>
                    <!-- 二级标题 -->
                    <div class="catalogue-second-box" :class="{ 'expanded': item.isExpanded }">
                        <div v-for="(child, childIndex) in item.children" :key="childIndex" class="catalogue-second"
                            :class="{ 'active': child.isActive }" @click="selectSecondLevel(index, childIndex)">
                            <div>
                                {{ child.name }}
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <!-- 展示PPT的主体部分 -->
            <div class="choosePPT-showContent">
                <div v-if="showContentList.length > 0">
                    <div v-for="(group, index) in showContentList" :key="index" class="ppt-group">
                        <div class="ppt-group-name">{{ group.name }}</div>
                        <div class="ppt-images-container">
                            <div v-for="(image, imgIndex) in group.images" :key="imgIndex" class="ppt-image-box">
                                <input type="checkbox" v-model="image.isChecked" @change="handleCheckboxChange(image)"
                                    class="custom-checkbox">
                                <img :src="image.src" alt="" class="ppt-image" @error="handleImageError"
                                    @load="handleImageLoad">
                                <div v-if="image.page" class="page-number">第{{ image.page }}页</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="no-content">
                    请选择目录查看PPT内容
                </div>
            </div>
            <!-- 已经选中的PPT -->
            <div class="choosePPT-choosed">
                <div class="choosed-title">已选择的PPT</div>
                <div v-if="choosedList.length > 0" class="choosed-content">
                    <div v-for="(group, index) in choosedList" :key="index" class="choosed-group">
                        <div class="choosed-group-name">{{ group.name }}</div>
                        <div class="choosed-images">
                            <div v-for="(image, imgIndex) in group.images" :key="imgIndex" class="choosed-image-box">
                                <div class="delete-btn" @click="deleteImage(group.name, image)">×</div>
                                <img :src="image.src" alt="" class="choosed-image">
                                <div v-if="image.page" class="choosed-page-number">第{{ image.page }}页</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="choosed-empty">
                    暂无选择的PPT
                </div>
                <div class="btn-box" @click="confirmSelection">确定</div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch, onMounted } from "vue";
export default defineComponent({
    name: 'choosePPT',
    components: {},
    props: {
        pptList: {
            type: Array,
            default: () => []
        }
    },

    setup(props, { emit }) {
        const data = reactive({
            pptList: props.pptList,
            catalogueList: [],//左侧目录
            showContentList: [],//展示PPT的主体部分
            choosedList: [],//已经选中的PPT
        });

        const initData = () => {
            data.catalogueList = data.pptList.map(item => {
                return {
                    name: item.name ? item.name : null,
                    isExpanded: true, // 默认展开所有二级目录
                    isActive: false, // 一级目录选中状态
                    children: item.children ? item.children.map(child => ({
                        name: child.name ? child.name : null,
                        id: child.id ? child.id : null,
                        isActive: false, // 二级目录选中状态
                        PPTList: child.PPTList || [] // 保存PPTList
                    })) : [],
                    PPTList: item.PPTList || [] // 保存PPTList
                }
            })
            console.log(`data.catalogueList`, data.catalogueList);
        }

        // 处理二级目录的PPTList，将同一目录下的图片分组
        const processChildrenPPTList = (children) => {
            const groupedList = [];
            children.forEach(child => {
                if (child.PPTList && child.PPTList.length > 0) {
                    const images = child.PPTList.map((ppt, index) => {
                        const imageObj = {
                            src: ppt.img || ppt, // 兼容新旧数据结构
                            page: ppt.page || null,
                            originalIndex: index,
                            isChecked: false,
                            categoryId: child.id // 添加二级目录的id
                        };
                        console.log('处理二级目录图片对象:', imageObj);
                        return imageObj;
                    });
                    groupedList.push({
                        name: child.name,
                        id: child.id, // 添加二级目录的id
                        images: images
                    });
                }
            });
            return groupedList;
        }

        // 切换展开/收起状态
        const toggleExpand = (index) => {
            data.catalogueList[index].isExpanded = !data.catalogueList[index].isExpanded;
        }

        // 选择一级目录
        const selectFirstLevel = (index) => {
            // 清除所有选中状态
            data.catalogueList.forEach((item, i) => {
                item.isActive = i === index;
                if (item.children) {
                    item.children.forEach(child => {
                        child.isActive = false;
                    });
                }
            });

            // 设置showContentList为选中目录的PPTList
            const selectedItem = data.catalogueList[index];
            console.log('选中的一级目录:', selectedItem);

            if (selectedItem.name === '方案概述') {
                // 方案概述也使用组格式
                data.showContentList = [{
                    name: selectedItem.name,
                    id: '方案概述', // 方案概述的id
                    images: selectedItem.PPTList.map((ppt, index) => ({
                        src: ppt.img || ppt, // 兼容新旧数据结构
                        page: ppt.page || null,
                        originalIndex: index,
                        isChecked: false,
                        categoryId: '方案概述' // 方案概述的id
                    }))
                }];
            } else {
                // 如果不是方案概述，则展示该一级标题下所有二级标题的图片和标题
                if (selectedItem.children && selectedItem.children.length > 0) {
                    data.showContentList = processChildrenPPTList(selectedItem.children);
                } else {
                    data.showContentList = [];
                }
            }

            // 更新复选框状态，反映已选择的图片
            updateCheckboxStates();
            console.log('最终的showContentList:', data.showContentList);
        }

        // 选择二级目录
        const selectSecondLevel = (firstIndex, secondIndex) => {
            // 清除所有选中状态
            data.catalogueList.forEach((item, i) => {
                item.isActive = i === firstIndex; // 选中对应的一级目录
                if (item.children) {
                    item.children.forEach((child, j) => {
                        child.isActive = (i === firstIndex && j === secondIndex);
                    });
                }
            });

            // 设置showContentList为选中二级目录的PPTList
            const selectedChild = data.catalogueList[firstIndex].children[secondIndex];
            // 将单个二级目录包装成组格式
            data.showContentList = [{
                name: selectedChild.name,
                id: selectedChild.id, // 添加二级目录的id
                images: selectedChild.PPTList.map((ppt, index) => ({
                    src: ppt.img || ppt, // 兼容新旧数据结构
                    page: ppt.page || null,
                    originalIndex: index,
                    isChecked: false,
                    categoryId: selectedChild.id // 添加二级目录的id
                }))
            }];

            // 更新复选框状态，反映已选择的图片
            updateCheckboxStates();
        }

        // 更新复选框状态，反映已选择的图片
        const updateCheckboxStates = () => {
            data.showContentList.forEach(group => {
                group.images.forEach(img => {
                    // 检查该图片是否在已选择列表中
                    const isInChoosedList = data.choosedList.some(choosedGroup =>
                        choosedGroup.name === group.name &&
                        choosedGroup.images.some(choosedImg =>
                            choosedImg.src === img.src &&
                            choosedImg.originalIndex === img.originalIndex
                        )
                    );
                    img.isChecked = isInChoosedList;
                });
            });
        }

        // 删除已选择的图片
        const deleteImage = (groupName, image) => {
            console.log('删除图片:', groupName, image);

            // 从已选择列表中移除该图片
            const groupIndex = data.choosedList.findIndex(group => group.name === groupName);
            if (groupIndex !== -1) {
                const imageIndex = data.choosedList[groupIndex].images.findIndex(img =>
                    img.src === image.src && img.originalIndex === image.originalIndex
                );
                if (imageIndex !== -1) {
                    data.choosedList[groupIndex].images.splice(imageIndex, 1);

                    // 如果该组没有图片了，移除整个组
                    if (data.choosedList[groupIndex].images.length === 0) {
                        data.choosedList.splice(groupIndex, 1);
                    }
                }
            }

            // 创建一个包含组名的图片对象传递给重置函数
            const imageWithName = {
                ...image,
                name: groupName
            };

            // 重置该图片在所有显示列表中的复选框状态
            resetImageCheckboxState(imageWithName);

            console.log('删除后的已选择列表:', data.choosedList);
        }

        // 重置图片的复选框状态
        const resetImageCheckboxState = (targetImage) => {
            // 只重置当前显示的图片中匹配的复选框状态
            data.showContentList.forEach(group => {
                group.images.forEach(img => {
                    // 只有当图片的src和originalIndex都匹配，且组名也匹配时才重置
                    if (img.src === targetImage.src &&
                        img.originalIndex === targetImage.originalIndex &&
                        group.name === targetImage.name) {
                        img.isChecked = false;
                    }
                });
            });
        }

        // 处理图片加载错误
        const handleImageError = (event) => {
            console.error('图片加载失败:', event.target.src);
        }

        // 处理图片加载成功
        const handleImageLoad = (event) => {
            console.log('图片加载成功:', event.target.src);
        }

        // 处理复选框变化
        const handleCheckboxChange = (image) => {
            console.log('复选框变化:', image);
            console.log('当前showContentList:', data.showContentList);
            updateChoosedList();
        }

        // 更新已选择的列表
        const updateChoosedList = () => {
            const choosedMap = new Map();

            // 先保留之前已选择的图片
            data.choosedList.forEach(group => {
                choosedMap.set(group.name, [...group.images]);
            });

            // 遍历所有显示的图片，更新选中状态
            data.showContentList.forEach(group => {
                console.log('处理组:', group.name, group.images);

                if (!choosedMap.has(group.name)) {
                    choosedMap.set(group.name, []);
                }

                const existingImages = choosedMap.get(group.name);

                // 处理当前显示的图片
                group.images.forEach(img => {
                    if (img.isChecked) {
                        // 检查是否已经存在
                        const isDuplicate = existingImages.some(existingImg =>
                            existingImg.src === img.src &&
                            existingImg.originalIndex === img.originalIndex
                        );
                        if (!isDuplicate) {
                            existingImages.push(img);
                        }
                    } else {
                        // 如果取消勾选，从已选择列表中移除
                        const index = existingImages.findIndex(existingImg =>
                            existingImg.src === img.src &&
                            existingImg.originalIndex === img.originalIndex
                        );
                        if (index !== -1) {
                            existingImages.splice(index, 1);
                        }
                    }
                });
            });

            // 转换为数组格式，只保留有图片的组
            data.choosedList = Array.from(choosedMap.entries())
                .filter(([name, images]) => images.length > 0)
                .map(([name, images]) => ({
                    name: name,
                    images: images
                }));

            console.log('已选择的列表:', data.choosedList);
        }

                // 确认选择
        const confirmSelection = () => {
            console.log('=== 已选择的PPT内容 ===');
            console.log('选择的总组数:', data.choosedList.length);
            
            if (data.choosedList.length === 0) {
                console.log('暂无选择的PPT内容');
                return [];
            }

            // 按title分组，收集每个目录下选中的页码和id
            const resultMap = new Map();
            
            data.choosedList.forEach((group) => {
                group.images.forEach((image) => {
                    const title = group.name; // 使用title作为唯一标识符
                    if (!resultMap.has(title)) {
                        resultMap.set(title, {
                            pages: [],
                            id: image.categoryId
                        });
                    }
                    
                    // 只添加有页码的图片
                    if (image.page) {
                        const pageArray = resultMap.get(title).pages;
                        if (!pageArray.includes(image.page)) {
                            pageArray.push(image.page);
                        }
                    }
                });
            });

            // 转换为最终格式
            const result = Array.from(resultMap.entries()).map(([title, data]) => ({
                id: data.id,
                title: title,
                arr: data.pages
            }));

            console.log(`result`, result);
            return result;
        }

        onMounted(() => {
            console.log(`props.pptList`, props.pptList);
            initData()
            // 默认选择方案概述
            if (data.catalogueList.length > 0) {
                selectFirstLevel(0); // 选择第一个目录（方案概述）
            }
        })
        return {
            ...toRefs(data),
            toggleExpand,
            selectFirstLevel,
            selectSecondLevel,
            handleCheckboxChange,
            deleteImage,
            handleImageError,
            handleImageLoad,
            confirmSelection,
        };
    },
});
</script>
<style lang="scss">
.choosePPT {
    padding-top: 20px;

    .choosePPT-title {
        text-align: center;
        width: 100%;
        font-size: 20px;
    }

    .choosePPT-content {
        height: 500px;

        .choosePPT-catalogue {
            width: 200px;
            padding: 0px 10px 0px 10px;

            // background-color: skyblue;
            .catalogue-first {
                width: 100%;
                font-size: 20px;
                font-weight: bold;
                background-color: #f0f0f0;
                padding: 10px;
                cursor: pointer;
                transition: background-color 0.3s ease;

                &:hover {
                    background-color: #e0e0e0;
                }

                &.active {
                    background-color: #8ebae9;
                    color: white;
                }

                .arrow {
                    transition: transform 0.3s ease;

                    &.arrow-rotate {
                        transform: rotate(90deg);
                    }
                }
            }

            .catalogue-second-box {
                background-color: #fdf5f5;
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;

                &.expanded {
                    max-height: 500px; // 根据实际内容调整
                }

                .catalogue-second {
                    width: 100%;
                    padding: 10px 20px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background-color 0.3s ease;

                    &:hover {
                        background-color: #f0e0e0;
                    }

                    &.active {
                        background-color: #eeceeb;
                        color: white;
                    }
                }
            }
        }

        .choosePPT-showContent {
            width: 500px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;

            .ppt-group {
                margin-bottom: 20px;
                padding: 15px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                .ppt-group-name {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    text-align: center;
                }

                .ppt-images-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    justify-content: center;

                    .ppt-image-box {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        .custom-checkbox {
                            position: absolute;
                            top: 5px;
                            left: 5px;
                            z-index: 10;
                            width: 16px;
                            height: 16px;
                            cursor: pointer;
                        }

                        .ppt-image {
                            width: 180px;
                            height: 160px;
                            object-fit: cover;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        }

                        .page-number {
                            position: absolute;
                            bottom: 5px;
                            left: 5px;
                            background-color: rgba(0, 0, 0, 0.7);
                            color: white;
                            padding: 2px 6px;
                            border-radius: 3px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                    }
                }
            }

            .no-content {
                text-align: center;
                color: #6c757d;
                font-size: 16px;
                padding: 40px 0;
            }
        }

        .choosePPT-choosed {
            width: 260px;
            background-color: #f8f9fa;
            padding: 15px;
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;

            .choosed-title {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
                padding: 10px;
                background-color: white;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .choosed-content {
                flex: 1;
                overflow-y: auto;
                margin-bottom: 60px; // 为底部按钮留出空间

                .choosed-group {
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: white;
                    border-radius: 6px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                    .choosed-group-name {
                        font-size: 14px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                        padding: 5px;
                        background-color: #e9ecef;
                        border-radius: 3px;
                        text-align: center;
                    }

                    .choosed-images {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 5px;
                        justify-content: center;

                        .choosed-image-box {
                            position: relative;

                            .delete-btn {
                                position: absolute;
                                top: -5px;
                                right: -5px;
                                width: 20px;
                                height: 20px;
                                background-color: #ff4757;
                                color: white;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: bold;
                                z-index: 10;
                                transition: background-color 0.2s ease;

                                &:hover {
                                    background-color: #ff3742;
                                }
                            }

                            .choosed-image {
                                width: 100px;
                                height: 80px;
                                object-fit: cover;
                                border: 1px solid #ddd;
                                border-radius: 3px;
                                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                            }

                            .choosed-page-number {
                                position: absolute;
                                bottom: 2px;
                                left: 2px;
                                background-color: rgba(0, 0, 0, 0.7);
                                color: white;
                                padding: 1px 4px;
                                border-radius: 2px;
                                font-size: 10px;
                                font-weight: bold;
                            }
                        }
                    }
                }
            }

            .choosed-empty {
                text-align: center;
                color: #6c757d;
                font-size: 14px;
                padding: 40px 0;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .btn-box {
                position: absolute;
                bottom: 15px;
                left: 15px;
                right: 15px;
                height: 40px;
                background-color: #007bff;
                color: #fff;
                text-align: center;
                line-height: 40px;
                cursor: pointer;
                border-radius: 5px;
                z-index: 10;
            }
        }
    }
}
</style>