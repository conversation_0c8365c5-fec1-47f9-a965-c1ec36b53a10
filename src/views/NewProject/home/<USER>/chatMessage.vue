<template>
    <div style="font-size: 16px;" :class="['message-container', isUser ? 'user-message' : 'ai-message']">
        <!-- 用户消息，头像在右边 -->
        <div v-if="isUser" class="margin_t_10">
            <div class="flex user-box">
                <div class="message">{{ message }}</div>
                <div>
                    <img v-if="userInfo.sex == '女'" class="avatar margin_l_24" style="width: 40px;height: 40px;"
                        src="@/assets/images/woman.png" alt="用户头像" />
                    <img v-else class="avatar margin_l_24" style="width: 40px;height: 40px;"
                        src="@/assets/images/man.png" alt="用户头像" />
                </div>
            </div>
        </div>
        <div v-else class="margin_t_10">
            <div class="flex ai-box">
                <div>
                    <img style="width: 40px;height: 40px;margin-right: 24px;" src="@/assets/images/AI/ai.png" alt="">
                </div>
                <!-- 多轮对话阶段，只有answer，没有keyword等等返回值，AI说什么就展示什么 -->
                <div class="message">
                    <div class="words" style="margin-bottom: -6px;" v-if="AIData.answer && !AIData.aim">
                        {{ writingAIAnswerText }}
                    </div>
                    <!-- 用于回显有定制记录的AI回答 -->
                    <div v-if="historyShowAIData">
                        <div v-if="historyShowAIData.busOperate == '定制'">
                            <div v-if="historyShowAIData.busDirect == '行业'">
                                <div v-if="historyShowAIData.hasSolution" style="padding: 10px 0px 10px 0px;">
                                    <!-- 搜到方案的情况 -->
                                    <!-- <div class="words">
                                        正在分析用户关键词......
                                    </div>
                                    <br />
                                    <div class="words">
                                        {{ '已分析用户需求，即将开始' + historyShowAIData.solutionName + '方案的生成:' }}
                                    </div> -->
                                    <div class="words fontBold">
                                        一、方案概述
                                    </div>
                                    <div class="words textIndent30">
                                        方案概述PPT已生成，PPT内容如下
                                    </div>
                                    <div style="font-size: 15px;color: #acacac;"
                                        class="words textIndent30 padding_l_32 padding_r_32">
                                        {{ historyShowAIData.solutionOverview }}
                                    </div>
                                    <br
                                        v-if="historyShowAIData.abilityList.length + historyShowAIData.noSearchedAbilityList.length !== 0">
                                    <div class="words fontBold"
                                        v-if="historyShowAIData.abilityList.length + historyShowAIData.noSearchedAbilityList.length !== 0">
                                        二、应用场景
                                    </div>
                                    <div v-if="historyShowAIData.goSearchAbilityList.length !== 0 && (historyShowAIData.abilityList.length + historyShowAIData.noSearchedAbilityList.length !== 0)"
                                        class="words padding_l_32 padding_r_32">
                                        {{ '思考完成，小麟即将为您的' + historyShowAIData.solutionName + '定制以下应用场景PPT：' }}
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in historyShowAIData.abilityList" :key="index">
                                        <div class="flex item-container">
                                            <div style="flex: 1;">
                                                <span>
                                                    {{ index + 1 + '、' }}
                                                </span>
                                                <span style="font-weight: bold;">
                                                    {{ item.name }}：
                                                </span>
                                                <span style="font-size: 15px;color: #acacac;">
                                                    {{ item.description }}
                                                </span>
                                            </div>
                                            <div class="redPoint"
                                                v-if="item.isNew && showHistoryLoadingDeleteBtn() && !isSBchange && historyShowAIData.isNewestChat">
                                            </div>
                                            <!-- 暂时隐藏删除 -->
                                            <div v-if="showHistoryLoadingDeleteBtn() && !isSBchange && historyShowAIData.isNewestChat"
                                                style="font-size: 16px;"
                                                class="flex just-center align-center pointer delete-btn"
                                                @click="deleteHistoryThisOne(item, 'thinkAbility')">
                                                删除
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 暂时隐藏 -->
                                    <div v-if="historyShowAIData.noSearchedAbilityList.length !== 0">
                                        <div class="words padding_l_32 padding_r_32 margin_t_10">
                                            小麟认为方案中可能还应该包含如下内容，但因训练内容仍在持续丰富当中，现在暂未能帮您定制这些内容。您可根据实际需要另行补充，小麟将继续努力学习，尽快提高自身能力!
                                        </div>
                                        <div class="padding_l_32 padding_r_32 margin_t_10"
                                            v-for="(item, index) in historyShowAIData.noSearchedAbilityList"
                                            :key="index">
                                            <div>
                                                <span>
                                                    {{ index + 1 + '、' }}
                                                </span>
                                                <span style="font-weight: bold;">
                                                    {{ item.name }}
                                                </span>
                                                <!-- <span v-if="!item.fromUser">
                                                    {{ '：' + item.reason }}
                                                </span> -->
                                            </div>
                                        </div>
                                    </div>
                                    <br
                                        v-if="historyShowAIData.peopleCaseList.length + historyShowAIData.solutionCaseList.length !== 0" />
                                    <div v-if="historyShowAIData.peopleCaseList.length + historyShowAIData.solutionCaseList.length !== 0"
                                        class="words fontBold">
                                        {{
                                            historyShowAIData.abilityList.length +
                                                historyShowAIData.noSearchedAbilityList.length
                                                !== 0 ? '三、应用案例' : '二、应用案例' }}
                                    </div>
                                    <!-- 用户说的案例 -->
                                    <div v-if="historyShowAIData.peopleCaseList.length !== 0">
                                        <div class="words padding_l_32 padding_r_32">
                                            {{ historyShowAIData.peopleCaseWords }}
                                        </div>
                                        <div class="padding_l_32 padding_r_32 margin_t_10"
                                            v-for="(item, index) in historyShowAIData.peopleCaseList" :key="index">
                                            <div class="flex item-container align-center">
                                                <div style="flex: 1;">
                                                    <span>
                                                        {{ index + 1 + '、' }}
                                                    </span>
                                                    <span style="font-weight: bold;">
                                                        {{ item.peopleWords }}
                                                    </span>
                                                    <span v-if="item.searched == '已搜到'">
                                                        {{ item.name }}
                                                    </span>
                                                    <span style="font-size: 15px;color: #acacac;"
                                                        :class="[item.searched == '已搜到' ? '' : 'unsearchedWord']">
                                                        {{ item.description }}
                                                    </span>
                                                </div>
                                                <div class="redPoint"
                                                    v-if="item.isNew && showHistoryLoadingDeleteBtn() && !isSBchange && item.id && historyShowAIData.isNewestChat">
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="showHistoryLoadingDeleteBtn() && !isSBchange && item.id && historyShowAIData.isNewestChat"
                                                    style="font-size: 16px;"
                                                    class="flex just-center align-center pointer delete-btn"
                                                    @click="deleteHistoryThisOne(item, 'thinkCase')">
                                                    删除
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <br v-if="historyShowAIData.peopleCaseList.length !== 0" />
                                    <div v-if="historyShowAIData.solutionCaseList.length !== 0"
                                        class="words padding_l_32 padding_r_32">
                                        小麟发现以下案例很适合您的方案，将为您加入定制：
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in historyShowAIData.solutionCaseList" :key="index">
                                        <div class="flex item-container align-center">
                                            <div style="flex: 1;">
                                                <span>
                                                    {{ index + 1 + '、' }}
                                                </span>
                                                <span style="font-weight: bold;">
                                                    {{ item.name }}：
                                                </span>
                                                <span style="font-size: 15px;color: #acacac;">
                                                    {{ item.description }}
                                                </span>
                                            </div>
                                            <div class="redPoint"
                                                v-if="item.isNew && showHistoryLoadingDeleteBtn() && !isSBchange && item.id && historyShowAIData.isNewestChat">
                                            </div>
                                            <!-- 暂时隐藏删除 -->
                                            <div v-if="showHistoryLoadingDeleteBtn() && !isSBchange && item.id && historyShowAIData.isNewestChat"
                                                style="font-size: 16px;"
                                                class="flex just-center align-center pointer delete-btn"
                                                @click="deleteHistoryThisOne(item, 'solutionCases')">
                                                删除
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="!historyShowAIData.hasSolution">
                                    <!-- AI3.0 -->
                                    <!-- <div>数据已查询完毕，我正在为您生成智慧校园方案方案的大纲:</div>
                                    <div class="outlineTextStyle padding_t_20 padding_l_20 padding_r_20">
                                        <div v-html="historyShowAIData.outlineText"></div>
                                        <h2>
                                            四、应用场景
                                        </h2>
                                        <div v-if="historyShowAIData.newNoSolutionShowThinkAbilityList.length !== 0">
                                            <div class="padding_l_16 padding_r_16 margin_t_10"
                                                v-for="(item, index) in historyShowAIData.newNoSolutionShowThinkAbilityList"
                                                :key="index">
                                                <div class="flex item-container">
                                                    <div style="flex: 1;">
                                                        <span>
                                                            {{ index + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.displayRealTitle }}
                                                        </span>
                                                        <span style="font-size: 15px;color: #acacac;"
                                                            :class="[item.searched == '已搜到' ? '' : 'unsearchedWord']">
                                                            {{ item.displayIntroduction }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class="padding_l_32 padding_r_32 margin_t_10">
                                                小麟希望为您的材料中编写以下方案场景，但小麟的知识库中似乎还没有用于编写这些方案场景的参考数据，得麻烦您自行补充了：
                                            </div>
                                            <div class="padding_l_32 padding_r_32 margin_t_10"
                                                v-for="(item, index) in historyShowAIData.newNoSolutionShowNoSearchAbilityList"
                                                :key="index">
                                                <div>
                                                    <span>
                                                        {{ index + 1 + '、' }}
                                                    </span>
                                                    <span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.displayText }}
                                                        </span>
                                                        <span>
                                                            {{ item.displayReason }}
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <h2 class="margin_t_20">
                                            五、应用案例
                                        </h2>
                                        <div class="padding_l_16 padding_r_16 margin_b_10 margin_t_10 flex item-container"
                                            v-for="(item, index) in historyShowAIData.newNoSolutionShowPeopleCasesList"
                                            :key="index">
                                            <div style="flex: 1;">
                                                <div class="margin_b_6">
                                                    <span style="font-weight: bold;margin-right: 10px;">
                                                        {{ item.displayText }}
                                                    </span>
                                                </div>
                                                <div v-for="(caseItem, caseIndex) in item.displayCaseList"
                                                    :key="caseIndex">
                                                    <div v-if="caseItem.hasReasult == '搜到了'">
                                                        <span>
                                                            {{ caseIndex + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;margin-right: 10px;">
                                                            {{ caseItem.displayCaseName }}
                                                        </span>
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ caseItem.displayCaseDescription }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div style="font-size: 15px;color: #acacac;">
                                                    {{ item.displayNoSearchWords }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex just-center align-center margin_t_6">
                                            <div v-if="geHistoryPPTLoading" class="loading-spinner"></div>
                                            <a-button v-else
                                                class="historyDownloadBtn margin_r_10 flex just-center align-center"
                                                @click="historyStreamChatContinue(historyShowAIData)">
                                                <div>下载方案</div>
                                            </a-button>
                                        </div>
                                    </div> -->
                                    <!-- AI2.0 -->
                                    <!-- 没搜到方案的情况 -->
                                    <div class="words margin_t_10">
                                        好的，小麟认为您的方案中可能包含以下内容：
                                    </div>
                                    <div v-if="historyShowAIData.abilityList.length !== 0"
                                        class="padding_l_32 padding_r_32 words fontBold margin_t_10">
                                        一、应用场景
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in historyShowAIData.abilityList" :key="index">
                                        <div v-if="item.searched">
                                            <div class="flex item-container align-center">
                                                <div style="flex: 1;">
                                                    <div>
                                                        <span>
                                                            {{ index + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.name }}
                                                        </span>
                                                        <span style="font-weight: bold;"
                                                            :class="[item.searched == '已搜到' ? 'searchedWord' : 'unsearchedWord']">
                                                            {{ item.realName }}
                                                        </span>
                                                    </div>
                                                    <div v-if="item.searched == '已搜到'">
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ item.description }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="redPoint"
                                                    v-if="item.isNew && historyShowAIData.goCustomizedList.length > 1 && item.searched == '已搜到' && !isSBchange && historyShowAIData.isNewestChat">
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="historyShowAIData.goCustomizedList.length > 1 && item.searched == '已搜到' && !isSBchange && historyShowAIData.isNewestChat"
                                                    style="font-size: 16px;"
                                                    class="flex just-center align-center pointer delete-btn"
                                                    @click="deleteHistoryThisOne(item, 'thinkAbility')">
                                                    删除
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="historyShowAIData.peopleCaseList.length !== 0"
                                        class="padding_l_32 padding_r_32 words fontBold margin_t_20">
                                        {{ historyShowAIData.abilityList.length !== 0 ? '二、' : '一、' }}应用案例
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in historyShowAIData.peopleCaseList" :key="index">
                                        <div v-if="item.searched">
                                            <div class="flex item-container align-center align-center">
                                                <div style="flex: 1;">
                                                    <div>
                                                        <span>
                                                            {{ index + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.name }}
                                                        </span>
                                                        <span style="font-weight: bold;"
                                                            :class="[item.searched == '已搜到' ? 'searchedWord' : 'unsearchedWord']">
                                                            {{ item.realName }}
                                                        </span>
                                                    </div>
                                                    <div v-if="item.searched == '已搜到'">
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ item.description }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="redPoint"
                                                    v-if="item.isNew && historyShowAIData.goCustomizedList.length > 1 && item.searched == '已搜到' && !isSBchange && item.id && historyShowAIData.isNewestChat">
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="historyShowAIData.goCustomizedList.length > 1 && item.searched == '已搜到' && !isSBchange && item.id && historyShowAIData.isNewestChat"
                                                    style="font-size: 16px;"
                                                    class="flex just-center align-center pointer delete-btn"
                                                    @click="deleteHistoryThisOne(item, 'thinkCase')">
                                                    删除
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="showHistoryContinueBtn">
                                    <!-- 暂时隐藏删除 -->
                                    <div v-if="historyShowAIData.hasSolution"
                                        class="words margin_t_10 padding_l_32 padding_r_32">
                                        <div>
                                            <span style="font-size: 16px;">
                                                小麟已为您定制好了方案，您可点击"
                                            </span>
                                            <span style="color: #007AFF;font-size: 16px;">
                                                确定
                                            </span>
                                            <span style="font-size: 16px;">
                                                "按钮去看看生成结果哦！
                                            </span>
                                        </div>
                                        <div v-if="showHistoryLoadingDeleteBtn()">
                                            <span style="font-size: 14px;">
                                                如有您不满意的地方，可动动鼠标去
                                            </span>
                                            <span style="color: red;font-size: 14px;">
                                                删除
                                            </span>
                                            <span style="font-size: 14px;">
                                                哦！
                                            </span>
                                        </div>
                                    </div>
                                    <div v-if="!historyShowAIData.hasSolution" class="words margin_t_10"
                                        style="padding: 0px 15px 0px 15px;">
                                        <div v-if="historyShowAIData.goCustomizedUniqueList.length == 0">
                                            抱歉，这个方案的内容小麟还没学过，未能为您定制成功，小麟将继续努力学习，争取早日帮到您！
                                        </div>
                                        <div v-else>
                                            <div>
                                                <span style="font-size: 16px;">
                                                    小麟已为您组合好了方案，您可点击"
                                                </span>
                                                <span style="color: #007AFF;font-size: 16px;">
                                                    确定
                                                </span>
                                                <span style="font-size: 16px;">
                                                    "按钮去看看生成结果哦！小麟也将继续努力学习，争取早日能为您定制完整方案！
                                                </span>
                                            </div>
                                            <!-- 暂时隐藏删除 -->
                                            <div v-if="historyShowAIData.goCustomizedList.length > 1">
                                                <span style="font-size: 14px;">
                                                    如有您不满意的地方，可动动鼠标去
                                                </span>
                                                <span style="color: red;font-size: 14px;">
                                                    删除
                                                </span>
                                                <span style="font-size: 14px;">
                                                    哦！
                                                </span>
                                            </div>
                                        </div>


                                    </div>
                                    <!-- <div v-if="!historyShowAIData.hasSolution && historyShowAIData.goCustomizedUniqueList.length == 0"
                                        class="words padding_l_32 padding_r_32 margin_t_10">
                                        抱歉，这个方案的内容小麟还没学过，未能为您定制成功，小麟将继续努力学习，争取早日帮到您
                                    </div> -->
                                    <div v-if="historyShowAIData.goCustomizedUniqueList.length !== 0"
                                        class="flex just-center align-center margin_t_6">
                                        <a-button class="sureBtn margin_r_10 flex just-center align-center">
                                            <div v-if="!historyContinueLoading" @click="historyContinue('1')">确定</div>
                                            <div v-else class="loading-spinner2"></div>
                                        </a-button>
                                        <!-- <a-button class="cancelBtn" @click="historyContinue('2')">取消</a-button> -->
                                    </div>
                                </div>
                            </div>
                            <!-- 商客 -->
                            <div v-if="historyShowAIData.busDirect == '商客'">
                                <div>
                                    <div class="words margin_b_10">
                                        {{ historyShowAIData.fixedWords1 }}
                                    </div>
                                    <div class="words margin_b_10">
                                        {{ historyShowAIData.fixedWords2 }}
                                    </div>
                                </div>
                                <div v-if="historyShowAIData.isRecommend == '是'">
                                    <div class="margin_b_10"
                                        v-for="(item, index) in historyShowAIData.choosedProductPackageList"
                                        :key="index">
                                        <div style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">{{
                                            numberToChinese(index + 1) + '、' +
                                            item.name + '产品'
                                            }}</div>
                                        <div v-for="(product, pIndex) in item.demandProductList" :key="pIndex"
                                            class="margin_b_4">
                                            <div>
                                                <span>
                                                    {{ pIndex + 1 + '、' }}
                                                </span>
                                                <span style="font-size: 16px;">
                                                    {{ product.name }}
                                                </span>
                                            </div>
                                            <!-- <div>{{ product.displayProductDescription }}</div> -->
                                        </div>
                                    </div>
                                    <div v-if="historyShowAIData.SupplementaryProductList.length !== 0">
                                        <div v-if="historyShowAIData.SupplementaryProductList.length !== 0"
                                            style="font-weight: bold;margin-top: 20px">
                                            {{ numberToChinese(historyShowAIData.choosedProductPackageList.length + 1) +
                                                '、补充产品'
                                            }}
                                        </div>
                                        <div v-if="historyShowAIData.SupplementaryProductList.length !== 0"
                                            v-for="(item, index) in historyShowAIData.SupplementaryProductList"
                                            :key="index">
                                            <div class="flex item-container">
                                                <div class="margin_r_10">
                                                    {{ index + 1 + '、' + item.name }}
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="!isSBchange" style="font-size: 16px;"
                                                    class="flex just-center align-center pointer delete-btn"
                                                    @click="deleteHistoryThisOne(item, 'productBag')">
                                                    删除
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                </div>
                                <div v-if="historyShowAIData.isRecommend == '否'">
                                    <div class="words" style="margin-top: 20px;">
                                        <span>
                                            场景包名称：
                                        </span>
                                        <span style="font-weight: bold;font-size: 18px;">
                                            {{ '《' + historyShowAIData.fixedWords3 + '场景包》' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="margin_t_10  padding_l_32 padding_r_32"
                                    v-for="(item, index) in historyShowAIData.freeCustomizedProductList" :key="index">
                                    <div>
                                        <span style="font-size: 18px;">
                                            {{ item.displayFixedText }}
                                        </span>
                                        <span style="font-weight: bold;">
                                            {{ item.displayText }}
                                        </span>
                                    </div>
                                    <div class="words textIndent30" style="font-size: 15px;color: #acacac;">
                                        {{ item.displayDescription }}
                                    </div>
                                    <div v-for="(product, pIndex) in item.productList" :key="pIndex"
                                        class="margin_b_4 margin_t_10 padding_l_32">
                                        <div v-if="product.searched == '已搜到'">
                                            <div class="words" style="font-weight: bold;font-size: 16px;">
                                                {{ pIndex + 1 + '、' + product.displayProductName }}
                                            </div>
                                            <div class="words textIndent30" style="font-size: 15px;color: #acacac;">
                                                {{ product.displayProductDescription }}
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="words" style="margin-top: 20px;padding-left: 32px;padding-right: 32px;">
                                    您的场景包已定制完成，是否查看？
                                </div>
                                <div class="flex just-center align-center margin_t_10">
                                    <a-button class="sureBtn margin_r_10 flex just-center align-center"
                                        :disabled="productPackageBtnDisabled" @click="historyContinue('1')">
                                        <div>确定</div>
                                        <!-- <div v-if="showGoProductLoading">确定</div>
                                        <div v-else class="loading-spinner2"></div> -->
                                    </a-button>
                                    <!-- <a-button class="cancelBtn" :disabled="productPackageBtnDisabled"
                                    @click="isGoProductPackage('2')">取消</a-button> -->
                                </div>
                            </div>
                            <!-- HDICT -->
                            <div v-if="historyShowAIData.busDirect == 'HDICT'">
                                <div v-if="historyShowAIData.HDICTCustomized.isJiangSu">
                                    <div class="words" v-if="!historyShowAIData.HDICTCustomized.isAIKeyword">
                                        我已明确您想定制一个HDICT方案，但是您的描述有点模糊，小麟未能理解清楚，可以麻烦您详细的描述您的需求吗，比如您的户型，有哪些房间，要什么产品？
                                    </div>
                                    <div v-if="historyShowAIData.HDICTCustomized.isAIKeyword">
                                        <div class="words">
                                            好的，我将为您直接定制一份智慧家庭方案
                                        </div>
                                        <div class="words">
                                            您的智慧家庭方案中已包含如下内容：
                                        </div>

                                        <div
                                            v-if="historyShowAIData.HDICTCustomized.writingHouseTypeList && historyShowAIData.HDICTCustomized.writingHouseTypeList.length !== 0">
                                            <div style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                                一、户型
                                            </div>
                                            <div v-for="(item, index) in historyShowAIData.HDICTCustomized.writingHouseTypeList"
                                                :key="index">
                                                <div>
                                                    {{ index + 1 + '、' + item.name }}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            v-if="historyShowAIData.HDICTCustomized.writingHDICTSceneList && historyShowAIData.HDICTCustomized.writingHDICTSceneList.length !== 0">
                                            <div style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                                {{ historyShowAIData.HDICTCustomized.HDICTSceneWord }}
                                            </div>
                                            <div v-for="(item, index) in historyShowAIData.HDICTCustomized.writingHDICTSceneList"
                                                :key="index">
                                                <div>
                                                    {{ index + 1 + '、' + item.name }}
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            v-if="historyShowAIData.HDICTCustomized.writingHDICTProductList && historyShowAIData.HDICTCustomized.writingHDICTProductList.length !== 0">
                                            <div style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                                {{ historyShowAIData.HDICTCustomized.HDICTProductWord }}
                                            </div>
                                            <div v-for="(item, index) in historyShowAIData.HDICTCustomized.writingHDICTProductList"
                                                :key="index">
                                                <div>
                                                    {{ index + 1 + '、' + item.name }}
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="!historyShowAIData.HDICTCustomized.isAISearchThing">
                                            很抱歉，小麟没有找到您想要的内容，小麟也将继续学习，争取早日为您找到您想要的内容！
                                        </div>
                                        <div v-if="historyShowAIData.HDICTCustomized.isAISearchThing">
                                            <div class="words">
                                                您的智慧家庭方案已经定制完成，是否查看？
                                            </div>
                                            <div class="flex just-center align-center margin_t_10">
                                                <a-button class="sureBtn margin_r_10 flex just-center align-center"
                                                    @click="HDICTCustomizedJump(historyShowAIData.HDICTCustomized.jumpResult)">
                                                    <div v-if="HDICTCustomizedHistoryLoading">确定</div>
                                                    <div v-else class="loading-spinner2"></div>
                                                </a-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else>
                                    小麟在为您定制时，发现您的账号权限目前只能使用行业与商客条线的功能，暂时不能使用HDICT功能。很抱歉这次没能帮到您！
                                </div>

                            </div>
                        </div>
                        <div v-if="historyShowAIData.busOperate == '检索'">
                            <div v-if="historyShowAIData.busDirect == 'HDICT'">
                                {{ historyShowAIData.aim }}
                                <div v-if="historyShowAIData.isHDICTSearched" class="pointer" style="color: #236CFF;"
                                    @click="jumpToHDICT(historyShowAIData.HDICTJumpKeyword, historyShowAIData.HDICTJumpType)">
                                    点击跳转→
                                </div>
                                <div v-else>
                                    我已明确您想检索一个HDICT方案，但是您的描述有点模糊，小麟未能理解清楚，可以麻烦您详细的描述您的需求吗，比如您的户型，有哪些房间，要什么产品？
                                </div>
                            </div>
                            <div v-else>
                                <div>
                                    {{ historyShowAIData.aim }}
                                </div>
                                <div v-if="historyShowSearchBtn(historyShowAIData.goSearchPageList)" class="pointer"
                                    @click="jumpToSearch(historyShowAIData.goSearchPageList)" style="color: #236CFF;">
                                    点击跳转→
                                </div>
                                <div v-else>
                                    很抱歉，小麟没能找到您想要的内容，小麟将继续努力学习，争取早日帮到您！
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 多轮对话结束，已经获取到了意图 AIData.busOperate：0检索 1定制 -->
                    <div class="words" v-if="showAnalysisWords">
                        {{ writingGetIntentionTextText }}
                    </div>
                    <!-- <br v-if="showBr1" /> -->
                    <!-- 此时是检索 -->
                    <!-- HDICT的检索 -->
                    <div v-if="AIData.busOperate == '检索'">
                        <div v-if="AIData.busDirect == 'HDICT'">
                            <div class="words">
                                {{ writingHDICTSearchNoDataText }}
                            </div>
                            <div class="words">
                                {{ writingHDICTJumpingText }}
                            </div>
                        </div>
                        <div class="words" v-if="AIData.busOperate == '检索'">
                            <div>
                                {{ writingAIAnswerSearchOverText }}
                            </div>
                            <div v-if="jumpingSearch" class="loading-spinner"></div>
                        </div>
                    </div>
                    <!-- 此时是定制 -->
                    <div v-if="AIData.busOperate == '定制'">
                        <!-- 定制行业方案 -->
                        <div v-if="AIData.busDirect == '行业'">
                            <div class="guideWordsContent" :class="!showGuideWords ? 'guideWordsHeight0' : ''">
                                <div class="words">
                                    {{ writingPromptChooseSolution }}
                                </div>
                                <div style="font-size: 16px;" class="words margin_b_4">
                                    <span>
                                        {{ writingGuideWords1 }}
                                    </span>
                                    <span style="color: rgb(0,0,255);">
                                        {{ writingGuideWords2 }}
                                    </span>
                                    <span>
                                        {{ writingGuideWords3 }}
                                    </span>
                                </div>
                                <div class="words">
                                    <span>
                                        {{ writingGuideWords4 }}
                                    </span>
                                    <span style="color: #fc00ff;">
                                        {{ writingGuideWords5 }}
                                    </span>
                                    <span>
                                        {{ writingGuideWords6 }}
                                    </span>
                                </div>
                            </div>
                            <!-- 搜到了方案 -->
                            <div v-if="nowSolutionId">
                                <div class="words margin_b_10">
                                    {{ writingfirstTitleText }}
                                </div>
                                <!-- 一、方案概述 -->
                                <h2 class="words">
                                    {{ writingSolutionOverview }}
                                </h2>
                                <!-- 正在生成xx方案概述... -->
                                <div v-if="!hasGetOverview" class="words textIndent30">
                                    {{ writingSolutionOverview2 }}
                                </div>
                                <div class="words textIndent30">
                                    <span class=" ">
                                        {{ writingSolutionOverview3 }}
                                    </span>
                                    <span class=" ">
                                        {{ writingSolutionOverview4 }}
                                    </span>
                                </div>
                                <div style="font-size: 15px;color: #acacac;"
                                    class="words textIndent30 padding_l_32 padding_r_32">
                                    {{ writingSolutionOverview5 }}
                                </div>
                                <br v-if="showBr2 && isAbilityList" />
                                <!-- 二、应用场景 -->
                                <h2 v-if="isAbilityList" class="words">
                                    <span class="words">
                                        {{ writingApplicationSceneText }}
                                    </span>
                                    <span style="color: red;">
                                        {{ writingApplicationSceneText2 }}
                                    </span>
                                </h2>
                                <div v-if="!hasGetElementPoint" class="words textIndent30">
                                    <div>
                                        {{ writingApplicationSceneText3 }}
                                    </div>
                                </div>
                                <div class="thinkingContent padding_l_32 padding_r_32"
                                    :class="isThinkingOver ? 'thinkingHeight0' : ''">
                                    <div>
                                        <div class="margin_b_10">
                                            {{ showPeopleThinkingWords }}
                                        </div>
                                        <div v-if="showThinkingArea">
                                            <div class="thinkingTextStyle" ref="thinkingTextRef2" v-html="thinkingText">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="isAbilityList" class="words textIndent30">
                                    {{ writingApplicationSceneText4 }}
                                </div>
                                <div class="padding_l_32 padding_r_32 margin_t_10"
                                    v-for="(item, index) in newShowSceneList" :key="index">
                                    <div v-if="item.searched" class="flex item-container align-center">
                                        <div style="flex: 1;">
                                            <span>
                                                {{ index + 1 + showSolutionSceneList.length + '、' }}
                                            </span>
                                            <span style="font-weight: bold;">
                                                {{ item.displayRealTitle }}
                                            </span>
                                            <span style="font-size: 15px;color: #acacac;"
                                                :class="[item.searched == '已搜到' ? '' : 'unsearchedWord']">
                                                {{ item.displayIntroduction }}
                                            </span>
                                        </div>
                                        <div class="redPoint"
                                            v-if="item.isNew && showLoadingDeleteBtn() && !isAIWriting && solutionStatus == '定制完成'">
                                        </div>
                                        <!-- 暂时隐藏删除 -->
                                        <div v-if="showLoadingDeleteBtn() && !isAIWriting && solutionStatus == '定制完成'"
                                            class="delete-btn"
                                            @click="deleteThisOne('thinkAbility', item.specialTitle, item.id, item.detail)">
                                            删除
                                        </div>
                                    </div>
                                </div>
                                <!-- 三、应用案例 -->
                                <h2 v-if="isShowCasesTitle" class="words">
                                    <br v-if="showBr3" />
                                    {{ writingApplicationCaseText }}
                                </h2>
                                <div v-if="!isNopeopleCase">
                                    <div class="words   padding_l_32 padding_r_32">
                                        {{ writingPeopleCasasText }}
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_b_10 margin_t_10 flex item-container align-center"
                                        v-for="(item, index) in showPeopleCasesList" :key="index">
                                        <div style="flex: 1;" v-if="item.searched">
                                            <span>
                                                {{ index + 1 + '、' }}
                                            </span>
                                            <span style="font-weight: bold;">
                                                {{ item.displayText }}
                                            </span>
                                            <span>
                                                {{ item.displayArrow }}
                                            </span>
                                            <span v-if="item.searched == '已搜到'">
                                                {{ item.displayRealTitle }}
                                            </span>
                                            <span style="font-size: 15px;color: #acacac;"
                                                :class="[item.searched == '已搜到' ? '' : 'unsearchedWord']">
                                                {{ item.displayIntroduction }}
                                            </span>
                                        </div>
                                        <div class="redPoint"
                                            v-if="item.isNew && showLoadingDeleteBtn() && !isAIWriting && item.id && solutionStatus == '定制完成'">
                                        </div>
                                        <!-- 暂时隐藏删除 -->
                                        <div v-if="showLoadingDeleteBtn() && !isAIWriting && item.id && solutionStatus == '定制完成'"
                                            class="delete-btn"
                                            @click="deleteThisOne('thinkCase', item.specialTitle, item.id, item)">
                                            删除
                                        </div>
                                    </div>
                                </div>
                                <div v-if="isShowCases">
                                    <div class="words   padding_l_32 padding_r_32">
                                        {{ writingApplicationCaseText2 }}
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10  flex item-container align-center"
                                        v-for="(item, index) in showSolutionCaseList" :key="index">
                                        <div style="flex: 1;">
                                            <span>
                                                {{ index + 1 + '、' }}
                                            </span>
                                            <span style="font-weight: bold;">
                                                {{ item.displayText }}
                                            </span>
                                            <span style="font-size: 15px;color: #acacac;">
                                                {{ item.displayDescription }}
                                            </span>
                                        </div>
                                        <div class="redPoint"
                                            v-if="item.isNew && showLoadingDeleteBtn() && !isAIWriting && solutionStatus == '定制完成'">
                                        </div>
                                        <!-- 暂时隐藏删除 -->
                                        <div v-if="showLoadingDeleteBtn() && !isAIWriting && solutionStatus == '定制完成'"
                                            class="delete-btn"
                                            @click="deleteThisOne('solutionCase', item.specialTitle, item.id, item)">
                                            删除
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <!-- 没搜到方案 -->
                            <div v-if="!nowSolutionId">
                                <div v-if="showNoSolutionAnalysisWords">
                                    <div class="words">
                                        {{ writingNoSolutionText }}
                                    </div>
                                    <div style="margin-bottom: -15px;" class="flex just-center align-center margin_t_6">
                                        <a-button v-if="showNoSolutionContinue"
                                            class="sureBtn margin_r_10 flex just-center align-center"
                                            @click="noSolutionContinue('1')">
                                            <div>确定</div>
                                        </a-button>
                                    </div>
                                </div>
                                <div class="words">
                                    {{ writingNoSolutionCustomized }}
                                </div>
                                <div v-if="showThinkingBox" class="thinkingContent padding_l_32 padding_r_32"
                                    :class="isThinkingOver ? 'thinkingHeight0' : ''">
                                    <div>
                                        <div class="margin_b_10">
                                            {{ showPeopleThinkingWords }}
                                        </div>
                                        <div v-if="showThinkingArea">
                                            <div class="thinkingTextStyle" ref="thinkingTextRef" v-html="thinkingText">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="!isNoNoSolutionAbility">
                                    <div class="words padding_l_32 padding_r_32 ">
                                        {{ writingNoSolutionCustomized2 }}
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in showNoSolutionThinkAbilityList" :key="index">
                                        <div v-if="item.searched">
                                            <div class="flex item-container align-center">
                                                <div style="flex: 1;">
                                                    <div>
                                                        <span>
                                                            {{ index + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.displayKey }}
                                                        </span>
                                                        <span style="font-weight: bold;"
                                                            :class="[item.searched == '已搜到' ? 'searchedWord' : 'unsearchedWord']">
                                                            {{ item.displayRealTitle }}
                                                        </span>
                                                    </div>
                                                    <div v-if="item.searched == '已搜到'">
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ item.displayIntroduction }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="redPoint"
                                                    v-if="item.isNew && showLoadingDeleteBtn() && item.searched == '已搜到' && !isAIWriting && !isSBchange && solutionStatus == '定制完成'">
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="showLoadingDeleteBtn() && item.searched == '已搜到' && !isAIWriting && !isSBchange && solutionStatus == '定制完成'"
                                                    class="delete-btn"
                                                    @click="deleteThisOne('thinkAbility', item.specialTitle, item.id, item)">
                                                    删除
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br v-if="showBr7" />
                                <div v-if="!isNoNosolutionCases">
                                    <div class="words   padding_l_32 padding_r_32 ">
                                        {{ writingNoSolutionCustomized3 }}
                                    </div>
                                    <div class="padding_l_32 padding_r_32 margin_t_10"
                                        v-for="(item, index) in showNoSolutionPeopleCasesList" :key="index">
                                        <div v-if="item.searched">
                                            <div class="flex item-container align-center">
                                                <div style="flex: 1;">
                                                    <div>
                                                        <span>
                                                            {{ index + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;">
                                                            {{ item.displayText }}
                                                        </span>
                                                        <span style="font-weight: bold;"
                                                            :class="[item.searched == '已搜到' ? 'searchedWord' : 'unsearchedWord']">
                                                            {{ item.displayRealTitle }}
                                                        </span>
                                                    </div>
                                                    <div v-if="item.searched == '已搜到'">
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ item.displayIntroduction }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="redPoint"
                                                    v-if="item.isNew && showLoadingDeleteBtn() && item.searched == '已搜到' && !isAIWriting && !isSBchange && item.id && solutionStatus == '定制完成'">
                                                </div>
                                                <!-- 暂时隐藏删除 -->
                                                <div v-if="showLoadingDeleteBtn() && item.searched == '已搜到' && !isAIWriting && !isSBchange && item.id && solutionStatus == '定制完成'"
                                                    class="delete-btn"
                                                    @click="deleteThisOne('thinkCase', item.specialTitle, item.id, item)">
                                                    删除
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- AI3.0 自主定制方案内容 -->
                                <div v-if="!nowSolutionId">
                                    <div class="words">
                                        {{ showPeopleWords1 }}
                                    </div>
                                    <div class="zhengceContent" :class="isInternetOver ? 'zhengceHeight0' : ''">
                                        <div>
                                            <div v-html="zhengceText"></div>
                                        </div>
                                    </div>
                                    <div v-if="showOutlineText" class="outlineTextStyle">
                                        <div class="line">
                                            {{ writingOutlineWords }}
                                        </div>
                                        <div v-if="!isOutlineTextOver" style="padding: 20px 20px 0px 20px;">
                                            <div v-html="outlineText"></div>
                                        </div>

                                        <!-- 大纲流式完成，转换成可编辑的格式 -->
                                        <div style="padding: 20px 20px 0px 20px;" v-if="isOutlineTextOver">
                                            <div v-for="(item, index) in pptJsonObj.children" :key="index">
                                                <!-- 大标题，level为1的 -->
                                                <div class="overviewOutlineTitle">
                                                    {{ item.content }}
                                                </div>
                                                <!-- 政策背景、需求分析、方案概述、应用场景 四大类，level为2的 -->
                                                <div class="margin_b_20"
                                                    v-for="(categoryItem, categoryIndex) in item.children"
                                                    :key="categoryIndex">
                                                    <div class="overviewOutlineCategoryTitle">
                                                        {{ categoryItem.content }}
                                                    </div>
                                                    <!-- 每个大类下小类的标题，level为3的 -->
                                                    <div class="margin_b_6"
                                                        v-for="(subItem, subIndex) in categoryItem.children"
                                                        :key="subIndex">
                                                        <div class="overviewOutlineSubTitle margin_b_10">
                                                            <!-- <a-input v-model:value="subItem.content"></a-input> -->
                                                            {{ subItem.content }}
                                                        </div>
                                                        <!-- 每个小类下的具体内容，level为4的 -->
                                                        <div v-for="(contentItem, contentIndex) in subItem.children"
                                                            :key="contentIndex">
                                                            <!-- <a-input v-model:value="contentItem.content"></a-input> -->
                                                            {{ contentItem.content }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h2 class="margin_l_20">
                                            <!-- 大标题：应用场景 -->
                                            {{ writingABigTitleScene }}
                                        </h2>
                                        <div class="thinkingContent" :class="isThinkingOver ? 'thinkingHeight0' : ''">
                                            <div style="padding: 0px 20px 0px 20px;">
                                                <div class="margin_b_10">
                                                    {{ showPeopleThinkingWords }}
                                                </div>
                                                <div v-if="showThinkingArea">
                                                    <div class="thinkingTextStyle" ref="thinkingTextRef3"
                                                        v-html="thinkingText"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="padding_l_32 padding_r_32 margin_t_10"
                                            v-for="(item, index) in showThinkAbilityList" :key="index">
                                            <div v-if="item.searched" class="flex item-container">
                                                <div style="flex: 1;">
                                                    <span>
                                                        {{ index + 1 + '、' }}
                                                    </span>
                                                    <span style="font-weight: bold;">
                                                        {{ item.displayRealTitle }}
                                                    </span>
                                                    <span style="font-size: 15px;color: #acacac;"
                                                        :class="[item.searched == '已搜到' ? '' : 'unsearchedWord']">
                                                        {{ item.displayIntroduction }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 什么场景都没搜到 -->
                                        <div class="padding_l_32 padding_r_32 margin_t_10">
                                            {{ noThinkingAbilityText }}
                                        </div>
                                        <div class="padding_l_32 padding_r_32 margin_t_10"
                                            v-for="(item, index) in showNoSearchAbilityList" :key="index">
                                            <div>
                                                <span>
                                                    {{ index + 1 + '、' }}
                                                </span>
                                                <span>
                                                    <span style="font-weight: bold;">
                                                        {{ item.displayText }}
                                                    </span>
                                                    <span>
                                                        {{ item.displayReason }}
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <h2 class="margin_l_20 margin_t_20">
                                            <!-- 大标题：应用案例 -->
                                            {{ writingABigTitleCase }}
                                        </h2>
                                        <div class="padding_l_32 padding_r_32 margin_b_10 margin_t_10 flex item-container"
                                            v-for="(item, index) in showPeopleCasesList" :key="index">
                                            <div style="flex: 1;">
                                                <div>
                                                    <span style="font-weight: bold;margin-right: 10px;">
                                                        {{ item.displayText }}
                                                    </span>
                                                </div>
                                                <div v-for="(caseItem, caseIndex) in item.displayCaseList"
                                                    :key="caseIndex">
                                                    <div v-if="caseItem.hasReasult == '搜到了'">
                                                        <span>
                                                            {{ caseIndex + 1 + '、' }}
                                                        </span>
                                                        <span style="font-weight: bold;margin-right: 10px;">
                                                            {{ caseItem.displayCaseName }}
                                                        </span>
                                                        <span style="font-size: 15px;color: #acacac;">
                                                            {{ caseItem.displayCaseDescription }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div style="font-size: 15px;color: #acacac;">
                                                    {{ item.displayNoSearchWords }}
                                                </div>
                                            </div>
                                        </div>
                                        <div style="padding-left: 10px; padding-bottom: 10px;">
                                            <div v-if="writingOutlineTextLoading" class="loading-spinner"></div>
                                        </div>
                                    </div>

                                    <div class="words margin_t_10">
                                        {{ showPeopleWords2 }}
                                    </div>
                                    <div v-if="showOutlineCustomizedBtn"
                                        class="flex just-center align-center margin_t_10">
                                        <div v-if="getPPTLoading" class="loading-spinner"></div>
                                        <a-button v-else type="primary" @click="confirmOutlineText">下载方案</a-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 定制商客方案 -->
                        <div v-if="AIData.busDirect == '商客'">
                            <!-- 用了推荐的方案 -->
                            <div class="words margin_b_10">
                                {{ writingAcceptRecommendText }}
                            </div>
                            <div class="words margin_b_10">
                                {{ writingAcceptRecommendText2 }}
                            </div>
                            <div class="margin_b_10" v-for="(item, index) in showProductPackageOwnList" :key="index">
                                <div style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">{{
                                    numberToChinese(index + 1) + '、' +
                                    item.displayText
                                    }}</div>
                                <div v-for="(product, pIndex) in item.demandProductList" :key="pIndex"
                                    class="margin_b_4">
                                    <div>
                                        <span v-if="product.canPrinting == '可以'">
                                            {{ pIndex + 1 + '、' }}
                                        </span>
                                        <span style="font-size: 16px;">
                                            {{
                                                product.displayProductName }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!isNoPeopleSayProduct">
                                <!-- X、补充产品 -->
                                <div class="words" style="margin-top: 20px;font-weight: bold;">
                                    {{ writingAddProductText }}
                                </div>
                                <div class="margin_t_10" v-for="(item, index) in showPeopleSayProductList" :key="index">
                                    <div v-if="item.searched == '已搜到'">
                                        <div class="flex item-container">
                                            <div class="margin_r_10">
                                                {{ index + 1 + '、' + item.displayText }}
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <!-- 用户自己定制 -->
                            <div class="words" style="margin-top: -20px;">
                                <!-- 正在分析需求并规划场景包的产品组合...... -->
                                {{ writingFreeCustomizedText }}
                            </div>
                            <!-- 已分析需求，即将开始xxx场景包的生成： -->

                            <div class="words margin_t_10">
                                {{ writingFreeCustomizedText2 }}
                            </div>
                            <div class="words margin_t_10">
                                <!-- 场景包名称： -->
                                <span>
                                    {{ writingFreeCustomizedText3 }}
                                </span>
                                <!-- 具体名称 -->
                                <span style="font-weight: bold;font-size: 18px;">
                                    {{ writingProductPackageName }}
                                </span>
                            </div>
                            <!-- 语义识别出来的产品 -->
                            <div class="margin_t_10  padding_l_32 padding_r_32"
                                v-for="(item, index) in showThinkProductList" :key="index">
                                <div>
                                    <span style="font-size: 18px;">
                                        {{ item.displayFixedText }}
                                    </span>
                                    <span style="font-weight: bold;">
                                        {{ item.displayText }}
                                    </span>
                                </div>
                                <div class="words textIndent30" style="font-size: 15px;color: #acacac;">
                                    {{ item.displayDescription }}
                                </div>
                                <div v-if="item.showLoadingWords" class="words" style="color: rgb(211 212 5 / 85%);">
                                    {{ item.loadingWords }}
                                </div>
                                <div v-for="(product, pIndex) in item.productList" :key="pIndex"
                                    class="margin_b_4 margin_t_10 padding_l_32">
                                    <div v-if="product.searched == '已搜到'">
                                        <div class="words" style="font-weight: bold;font-size: 16px;">
                                            {{ pIndex + 1 + '、' + product.displayProductName }}
                                        </div>
                                        <div class="words textIndent30" style="font-size: 15px;color: #acacac;">
                                            {{ product.displayProductDescription }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 总结语和确认定制按钮 -->
                            <div class="words" style="margin-top: 20px;padding-left: 32px;padding-right: 32px;">
                                {{ writingproductBagSummarizeText }}
                            </div>
                            <div class="flex just-center align-center margin_t_10"
                                v-if="showJumpProductBagCustomizedBtn">
                                <a-button class="sureBtn margin_r_10 flex just-center
                                align-center" :disabled="productPackageBtnDisabled" @click="isGoProductPackage('1')">
                                    <div v-if="showGoHDICTCustomizedProductLoading">确定</div>
                                    <div v-else class="loading-spinner2"></div>
                                </a-button>
                            </div>
                        </div>
                        <!-- 定制HDICT方案 -->
                        <div v-if="AIData.busDirect == 'HDICT'">
                            <!-- 是定制，但是keyword全为空 -->
                            <div
                                v-if="AIData.keyword.houseType.length + AIData.keyword.product.length + AIData.keyword.scene.length == 0">
                                {{ writingHDICTSearchNoDataText }}
                            </div>
                            <!-- 是定制，keyword不全为空 -->
                            <div v-else>
                                <div class="words">
                                    {{ writingHDICTText1 }}
                                </div>
                                <div class="words">
                                    {{ writingHDICTText2 }}
                                </div>
                                <!-- 户型 -->
                                <div class="margin_t_10" style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                    {{ writingHouseType1Text }}
                                </div>
                                <div class="margin_t_10" v-for="(item, index) in showHouseTypeList" :key="index">
                                    <div v-if="item.searched == '已搜到'">
                                        <div>
                                            {{ index + 1 + '、' + item.displayText }}
                                        </div>
                                    </div>
                                </div>
                                <!-- 场景 -->
                                <div class="margin_t_10" style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                    {{ writingScene1Text }}
                                </div>
                                <div class="margin_t_10" v-for="(item, index) in showHDICTSceneList" :key="index">
                                    <div v-if="item.searched == '已搜到'">
                                        <div>
                                            {{ index + 1 + '、' + item.displayText }}
                                        </div>
                                    </div>
                                </div>
                                <!-- 产品 -->
                                <div class="margin_t_10" style="font-weight: bold;margin-bottom: 6px;font-size: 18px;">
                                    {{ writingProduct1Text }}
                                </div>
                                <div class="margin_t_10" v-for="(item, index) in showHDICTProductList" :key="index">
                                    <div v-if="item.searched == '已搜到'">
                                        <div>
                                            {{ index + 1 + '、' + item.displayText }}
                                        </div>
                                    </div>
                                </div>
                                <div class="words margin_t_10">
                                    {{ writingHDICTCustomizedSummary }}
                                </div>
                                <div class="words margin_t_10">
                                    {{ writingHDICTCustomizedNoDataText }}
                                </div>
                                <div class="flex just-center align-center margin_t_10" v-if="showHDICTCustomizedBtn">
                                    <a-button class="sureBtn margin_r_10 flex just-center
                                    align-center" :disabled="goHDICTCustomizedBtnDisabled"
                                        @click="goHDICTCustomizedBtn()">
                                        <div v-if="showGoProductLoading">确定</div>
                                        <div v-else class="loading-spinner2"></div>
                                    </a-button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- 总结语 -->
                    <div>
                        <!-- <br v-if="showBr4" /> -->
                        <div class="words margin_t_10 padding_l_32 padding_r_32">
                            <div>
                                <span style="font-size: 16px;">
                                    {{ writingSummarizeText }}
                                </span>
                                <span style="color: #007AFF;font-size: 16px;">
                                    {{ writingSummarize2Text }}
                                </span>
                                <span style="font-size: 16px;">
                                    {{ writingSummarize3Text }}
                                </span>
                            </div>
                            <div v-if="showLoadingDeleteBtn()">
                                <span style="font-size: 14px;">
                                    {{ writingSummarize4Text }}
                                </span>
                                <span style="color: red;font-size: 14px;">
                                    {{ writingSummarize5Text }}
                                </span>
                                <span style="font-size: 14px;">
                                    {{ writingSummarize6Text }}
                                </span>
                            </div>
                        </div>
                        <div class="flex just-center align-center margin_t_6" v-if="showJumpCustomizedBtn">
                            <a-button :disabled="jumpBtnDisabled"
                                class="sureBtn margin_r_10 flex just-center align-center">
                                <div v-if="showGoCustomizedLoading" @click="jumpCustomized('1')">确定</div>
                                <div v-else class="loading-spinner2"></div>
                            </a-button>
                            <!-- <a-button :disabled="jumpBtnDisabled" class="cancelBtn"
                                @click="jumpCustomized('2')">取消</a-button> -->
                        </div>
                    </div>
                    <!-- AI回答完毕之前展示一个loading -->
                    <div v-if="isAIWriting" class="loading-spinner"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { defineComponent, onMounted, reactive, toRefs, watch, ref, nextTick, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getRetrieveList, getElementExpand, storeChatResult, getNewAssociate, updateChatResult, getChatJSON, getAIPPT, getPPTElementExpand, getPromptWords, newGo } from "@/api/AI/ai.js";
import { toShopList, addShopPro } from "@/api/buyList/index.js";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import bullshitSwitch from "../../../../utils/hideAIBullshit";
import { numberToChinese } from "@/utils/chineseNums";
import { message } from "ant-design-vue";
import { currentTab } from "@/store";
import { marked } from 'marked'
import getBaseUrl from "@/utils/baseUrl";
import { set } from "vue-demi";
import { DataAnalysis } from "@element-plus/icons-vue";
export default defineComponent({
    components: {
    },
    emits: [
        "changeResultType",
        "continueScroll",
        "closeSpeakBox",
        "noGood",
        "clearAISession",
        "showProductPackageDetailBox",
        "closeProductPackageDetailBox",
        "changeRightModuleType",
        "addPPT",
        "contorlAddingPic",
        "openPPTPreview",
        "closePPTPreview",
        "clearPPTList",
        "addPPTBigTitle",
        "addPPTSmallTitle",
        "updatePPT",
        "showHDICTBox",
        "contorlAddingHDICT",
        "addHDICTPics",
        "getChooseSolutionList",
        "chatCloseBox",
    ],
    props: {
        message: {
            type: String,
            required: null,
        },
        isUser: {
            type: Boolean,
            required: null,
        },
        AIData: {
            type: Object,
            required: {},
        },
        sessionId: {
            type: String,
            required: null,
        },
        thinkingStatus: {
            type: Boolean,
            required: true,
        },
    },
    setup(props, { emit }) {
        let typingWorker = null;
        let pendingResolvers = {};
        let itemMap = new Map(); // 全局 item 映射表
        let itemIdCounter = 0; // item ID 计数器
        const Router = useRouter();
        const Route = useRoute();
        const getCurTab = currentTab();
        const embedUrl = import.meta.env.VITE_EMBED_URL;
        const data = reactive({
            userInfo: JSON.parse(localStorage.getItem("userInfo")),
            isJiangSu: true,//是否为江苏公司用户(又放开了)
            isSBchange: false,//是不是有要临时修改的方案
            isChangeAI2: true,//是否是AI2.0
            thinkingStatus: props.thinkingStatus,//是否开启要素思考
            chatId: null,
            sessionId: props.sessionId,
            isFromHistory: false,
            showBr1: false,
            showBr2: false,
            showBr3: false,
            showBr4: false,
            showBr5: false,
            showBr6: false,
            showBr7: false,
            debugging: false,
            continueProcess: true,
            message: props.message,//用户说的话
            isUser: props.isUser,//判断是用户还是AI
            AIData: props.AIData,//AI返回的数据
            isAIWriting: false,//AI正在回答的loading效果
            AIAnswerText: '',
            writingAIAnswerText: '',
            AIAnswerIndex: 0,//只是在对话的打字机
            getIntentionText: '',
            writingGetIntentionTextText: '',
            getIntentionTextIndex: 0,//AI已经识别完是定制或者检索的开头语的打字机
            showAnalysisWords: true,//是否展示分析语
            showSolutionList: [],//展示的方案列表
            showShowSolutionList: [],//展示的方案列表
            nowSolutionDetail: null,
            nowSolutionId: null,//搜到了的方案的id
            nowSolutionName: '',//搜到了的方案的名称
            nowSolutionDescription: '',//搜到了的方案的简述
            goCustomizedList: [],//最终去定制的数组
            firstTitleText: '',
            writingfirstTitleText: '',
            firstTitleTextIndex: 0,//已分析用户需求，即将开始xxx方案的生成：
            writingPromptChooseSolution: '',
            promptChooseSolutionIndex: 0,
            showGuideWords: true,
            writingGuideWords1: '',
            guideWords1Index: 0,
            writingGuideWords2: '',
            guideWords2Index: 0,
            writingGuideWords3: '',
            guideWords3Index: 0,
            writingGuideWords4: '',
            guideWords4Index: 0,
            writingGuideWords5: '',
            guideWords5Index: 0,
            writingGuideWords6: '',
            guideWords6Index: 0,
            hasGetOverview: false,
            solutionOverviewText: '一、方案概述',//从这里开始是方案概述需要的东西
            writingSolutionOverview: '',
            solutionOverviewIndex: 0,
            solutionOverviewText2: '',
            writingSolutionOverview2: '',
            solutionOverviewIndex2: 0,
            solutionOverviewText3: '方案概述PPT已生成',
            writingSolutionOverview3: '',
            solutionOverviewIndex3: 0,
            solutionOverviewText4: '，PPT内容如下：',
            writingSolutionOverview4: '',
            solutionOverviewIndex4: 0,
            solutionOverviewText5: '',
            writingSolutionOverview5: '',
            solutionOverviewIndex5: 0,//方案概述正文（方案概述结束）
            applicationSceneText: '',//从这里开始是应用场景
            writingApplicationSceneText: '',
            applicationSceneIndex: 0,
            applicationSceneText2: '',//（场景顺序逻辑待优化）
            writingApplicationSceneText2: '',
            applicationSceneIndex2: 0,
            applicationSceneText3: '',
            writingApplicationSceneText3: '',
            applicationSceneIndex3: 0,
            applicationSceneText4: '',
            writingApplicationSceneText4: '',
            applicationSceneIndex4: 0,
            hasGetElementPoint: false,//是否已经拿到要素点
            solutionSceneList: [],//方案自带的场景
            solutionCaseList: [],//方案自带的案例
            showSolutionSceneList: [],//用于展示方案自带的场景
            showSolutionCaseList: [],//用于展示方案自带的案例
            goSearchAbilityList: [],//要素思考接口里获取的能力名称
            showThinkAbilityList: [],//用于展示思考出来的要素对应获取的能力
            noSearchThinkAbilityList: [],//没有搜到思考出来的要素的集合
            peopleSaidAbilityList: [],//人说的能力列表
            applicationCaseText: '三、应用案例',
            writingApplicationCaseText: '',
            applicationCaseIndex: 0,
            peopleCasesText: '',
            writingPeopleCasasText: '',
            peopleCasesIndex: 0,
            showPeopleCasesList: [],
            applicationCaseText2: '小麟发现以下案例很适合您的方案，将为您加入定制：',
            writingApplicationCaseText2: '',
            applicationCaseIndex2: 0,
            summarize: '',
            writingSummarizeText: '',//总结语
            summarizeIndex: 0,//小麟已为您定制好了方案，您可点击"
            writingSummarize2Text: '',
            summarize2Index: 0,//确定
            writingSummarize3Text: '',
            summarize3Index: 0,//"按钮去看看生成结果哦！
            writingSummarize4Text: '',
            summarize4Index: 0,//如有您不满意的地方，可动动鼠标去
            writingSummarize5Text: '',
            summarize5Index: 0,//删除
            writingSummarize6Text: '',
            summarize6Index: 0,//哦！
            showJumpCustomizedBtn: false,//定制完成是否查看的按钮
            jumpBtnDisabled: false,
            showGoCustomizedLoading: true,//去定制页面时候的确定按钮的loading
            noSolutionText: '暂时未匹配到完全契合的政策背景，我可以继续为您推荐相关的成功案例与解决方案模块，您希望继续吗',//没找到方案的话术
            writingNoSolutionText: '',
            noSolutionIndex: 0,
            showNoSolutionContinue: false,//展示没找到方案是否继续的按钮
            noSolutionCustomizedText: '好的，小麟认为您的方案中可能包含以下内容：',
            writingNoSolutionCustomized: '',
            noSolutionCustomizedIndex: 0,
            noSolutionCustomizedText2: '一、应用场景',
            writingNoSolutionCustomized2: '',
            noSolutionCustomizedIndex2: 0,
            showNoSolutionThinkAbilityList: [],//没搜到方案时候的要素展示
            noSolutionCustomizedText3: '二、应用案例',
            writingNoSolutionCustomized3: '',
            noSolutionCustomizedIndex3: 0,
            showNoSolutionPeopleCasesList: [],//没搜到方案时的案例展示
            AIAnswerSearchOverText: '检索已完成，正在打开结果页面',
            writingAIAnswerSearchOverText: '',
            AIAnswerSearchOverIndex: 0,//AI识别为检索后数据查询完毕的打字机
            goSearchPageList: {
                solutionList: [],
                abilityList: [],
                sceneList: [],
                productList: [],
                productPackageList: []
            },//===跳转到检索页面的数据===
            abilityNamesList: [],
            productBagsummarize: '您的场景包已定制完成，是否查看？',//商客总结语
            writingproductBagSummarizeText: '',
            productBagsummarizeIndex: 0,//商客总结语的打字机
            showJumpProductBagCustomizedBtn: false,
            productPackageBtnDisabled: false,//点过商客跳转是否的按钮以后禁用
            showGoProductLoading: true,//去产品包定制页面时候的确定按钮的loading
            historyContinueLoading: false,//聊天历史记录里的按钮的loading
            noSearchAbilityText: '小麟认为方案中可能还应该包含如下内容，但因训练内容仍在持续丰富当中，现在暂未能帮您定制这些内容。您可根据实际需要另行补充，小麟将继续努力学习，尽快提高自身能力!',
            writingNoSearchAbilityText: '',
            noSearchAbilityIndex: 0,
            showNoSearchAbilityList: [],
            productPackageListWords: [
                {
                    words: '正在分析用户需求，并规划商客应用场景......',
                },
                {
                    words: '已分析用户需求，即将开始方案的生成......',
                },
                {
                    words: '思考完成，小麟即将为您的方案匹配合适的产品......',
                },
            ],
            showProductPackageListWords: [],
            historyShowAIData: null,
            goHistoryData: {
                isNewestChat: true,//是否是最新的聊天记录
                isChat: null,
                chatId: null,
                isFree: null,//是否是自由定制
                peopleSaySolutionName: null,//用户说的方案
                hasSolution: null,//是否搜到了方案
                solutionName: null,//方案名称
                solutionOverview: null,//方案概述
                abilityList: [],//能力列表
                noSearchedAbilityList: [],//没有搜到的能力列表
                solutionCaseList: [],//方案自带的案例
                peopleCaseList: [],//用户说的案例
                peopleCaseWords: '',
                goCustomizedList: null,
                busOperate: null,
                busDirect: null,
                aim: null,
                goCustomizedUniqueList: [],//去定制的唯一列表
                isRecommend: '',//是否是根据推荐的定制的商客
                fixedWords1: '',//好的，我将为您直接定制一份个体民宿场景包
                fixedWords2: '',//您的个体民宿场景包中已包含如下产品
                fixedWords3: '',//场景包名称：《 xxxx场景包》
                choosedProductPackageList: [],//选择的产品包下的需求方案列表
                SupplementaryProductList: [],//补充内容
                goCustomizedProductBagList: [],//最终去定制的产品包的数组
                sceneList: [],//场景列表
                freeCustomizedProductList: [],//自由定制的产品列表
                PPTList: [],//PPT列表
                HDICTJSON: null,
                isHDICTSearched: null,//HDICT是否搜到了东西
                HDICTJumpType: null,//HDICT跳转检索时的类型
                HDICTJumpKeyword: null,//HDICT跳转检索时的关键词
                goSearchPageList: null,//普通检索的最终数据
                goSearchAbilityList: [],//普通检索的最终能力列表
                HDICTCustomized: {
                    isJiangSu: null,
                    jumpResult: null,//最终去定制的结果
                    isAIKeyword: null,//AI的Keyword是否有东西
                    isAISearchThing: null,//AI是否搜到了东西
                    houseTypeList: [],
                    writingHouseTypeList: [],
                    HDICTSceneWord: '',
                    HDICTSceneList: [],
                    HDICTProductWord: '',
                    HDICTProductList: [],
                    writingHDICTSceneList: [],
                    writingHDICTProductList: [],
                },
                HDICTList: [],
                pptJsonObj: {},
                newNoSolutionShowThinkAbilityList: [],//AI3.0的应用场景列表
                newNoSolutionShowNoSearchAbilityList: [],//AI3.0的没有场景的原因列表
                newNoSolutionShowPeopleCasesList: '',//AI3.0的案例列表
                outlineText: '',//详细大纲
                streamChatSessionId: null,//流式对话的sessionId
            },
            showHistoryContinueBtn: true,//展示聊天历史记录里的按钮
            productBagHasProduct: true,//商客定制页面是否包含产品
            displayedItems: [],
            showProductPackageOwnList: [],//从这里往下是商客定制的字段
            acceptRecommendIndex: 0,
            writingAcceptRecommendText: '',//接受了商客推荐的死话术打字机
            acceptRecommendIndex2: 0,
            writingAcceptRecommendText2: '',
            AIRecommendSceneList: [],//AI推荐的场景
            goCustomizedProductPackageList: [],//最终去定制的产品包的数组
            addProductIndex: 0,
            writingAddProductText: '',
            showPeopleSayProductList: [],//展示用户提到的产品
            freeCustomizedIndex: 0,
            writingFreeCustomizedText: '',//自由定制的起始句
            freeCustomizedIndex2: 0,
            writingFreeCustomizedText2: '',//已分析需求，即将开始xxx场景包的生成：
            freeCustomizedIndex3: 0,
            writingFreeCustomizedText3: '',//'场景包名称：'这五个字
            productPackageNameIndex: 0,//
            writingProductPackageName: '',//场景包名称：XXXX
            showThinkProductList: [],//用于展示的思考出来的产品列表
            HDICTJumpingText: '小麟正在为您跳转至HDICT定制页面......',
            writingHDICTJumpingText: '',
            HDICTJumpingIndex: 0,
            HDICTJumpType: null,//HDICT跳转检索时的类型
            HDICTJumpKeyword: null,//HDICT跳转检索时的关键词
            writingHDICTSearchNoDataText: '',//HDICT没有搜到东西的话术打字机
            HDICTSearchNoDataIndex: 0,
            cantCustomizeHDICTText: '小麟目前还在学习如何[定制]智慧家庭方案，所以现在小麟只能为您检索智慧家庭方案',
            writingCantCustomizeHDICTText: '',
            cantCustomizeHDICTIndex: 0,
            writingHDICTText1: '',//好的，我将为您直接定制一份个体民宿场景包
            HDICTText1Index: 0,
            writingHDICTText2: '',//您的个体民宿场景包中已包含如下产品
            HDICTText2Index: 0,
            houseType1Text: '一、户型', //一、户型
            writingHouseType1Text: '',
            houseType1Index: 0,
            houseTypeList: [],//搜出来的户型列表
            writingHouseTypeList: [],//用于打字的的户型列表
            showHouseTypeList: [],//用于展示的户型列表
            scene1Text: '', //二、场景
            writingScene1Text: '',
            scene1Index: 0,
            HDICTSceneList: [],//搜出来的场景列表
            showHDICTSceneList: [],//用于展示的场景列表
            HDICTProductText: '', //三、产品
            writingProduct1Text: '',
            product1Index: 0,
            HDICTProductList: [],//搜出来的产品列表
            writingHDICTProductList: [],//用于打字的产品列表
            showHDICTProductList: [],//用于展示的产品列表
            writingHDICTSceneList: [],//用于打字的的场景列表
            writingHDICTCustomizedSummary: '',
            HDICTCustomizedSummaryIndex: 0,
            writingHDICTCustomizedNoDataText: '',
            HDICTCustomizedNoDataIndex: 0,
            showHDICTCustomizedBtn: false,//去HDICT定制页面时候的按钮
            goHDICTCustomizedBtnDisabled: false,//去HDICT定制页面时候的按钮的禁用
            showGoHDICTCustomizedProductLoading: true,//去HDICT定制页面时候的确定按钮的loading
            HDICTCustomizedResult: {
                houseType: [],
                scene: [],
                product: [],
            },
            HDICTCustomizedHistoryLoading: true,//HDICT定制历史记录的loading
            houseTypeBrand: '',//户型品牌
            sceneBrand: '',//场景品牌
            isNopeopleCase: false,//是否没有用户说的案例
            isNoNosolutionCases: false,//没有方案时候的案例是否为空
            isNoNoSolutionAbility: false,//没有方案时候的能力是否为空
            isNoPeopleSayProduct: false,//是否没有用户说的产品
            isAbilityList: true,
            isShowCases: true,
            isShowCasesTitle: true,
            showWaitTextList: [],//打一些提示用户稍等的文字
            showNoSolutionWaitTextList: [],//打一些提示用户稍等的文字
            isShowNoSolutionWaitText: true,
            userCaseList: [],//用户说的案例
            solutionStatus: null,
            // 新的没有搜到方案的情况↓
            outlineText: '',//详细大纲
            showPeopleWords1: '',
            showPeopleWords1Index: 0,
            showPeopleWords2: '',
            showPeopleWords2Index: 0,
            showOutlineText: false,//要改成false!s
            writingOutlineTextLoading: false,
            writingOutlineWords: 'PPT大纲生成中...',
            isOutlineTextOver: false,//是否大纲生成完毕
            pptJsonObj: {},
            streamChatSessionId: null,//流式对话的sessionId
            showOutlineCustomizedBtn: false,//流式对话结束后的确认按钮
            zhengceText: '',//联网搜的政策的文本
            isInternetOver: false,//联网查询是否结束
            bigTitleSceneIndex: 0,
            writingABigTitleScene: '',
            bigTitleCaseIndex: 0,
            writingABigTitleCase: '',
            newNoSolutionCaseList: [],//没有搜到方案的时候的案例列表
            noThinkingAbilityText: '',
            noThinkingAbilityTextIndex: 0,
            getPPTLoading: false,
            geHistoryPPTLoading: false,
            streamPPTSource: null,//流式传输实例
            streamZhengceSource: null,//流式传输实例
            streamThinkingource: null,//流式传输实例
            streamElementRetrialSource: null,//流式传输实例
            thinkingText: '',//要素思考的流式文本
            isThinkingOver: false,//要素思考是否结束
            showPeopleThinkingWords: '',//要素思考的提示文本
            showPeopleThinkingWordsIndex: 0,
            showThinkingArea: false,
            showThinkingBox: false,
            thinkingOver: false,
            showBoxShadow: false,
            showNoSolutionAnalysisWords: true,
            messageQueue: [],
            isProcessingQueue: false,
            newShowSceneList: [],//流式返回的展示场景列表
            isQueueFinished: false,
            isSourceClosed: false,
        });
        const typeWrite = (item, prop, text) => {
            return new Promise(resolve => {
                let index = 0;
                const write = () => {
                    if (index < text.length) {
                        item[prop] += text.charAt(index);
                        index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                    }
                };
                write();
            });
        };
        function typeWriter(id, text, speed = 20) {
            return new Promise((resolve) => {
                data[id] = '';
                typingWorker.postMessage({
                    type: 'addToQueue',
                    item: { id, text, speed }
                });
                pendingResolvers[id] = resolve;
            });
        }
        // 通用data属性打字机
        // const typeWrite = (item, prop, text) => {
        //     return typeWriter(prop, text, 20);
        // };
        // 这次对话AI已经回答完毕
        const writingOver = async () => {
            // console.log('结束了')
            data.isAIWriting = false
            emit('writingOver')
        };
        // 只是单纯对话的打字机
        const typeWriterAIAnswer = () => {
            data.isAIWriting = true;
            return typeWriter('writingAIAnswerText', data.AIAnswerText, 20).then(() => {
                data.isAIWriting = false;
            });
        };

        // 获取了意图的打字机
        const typeWriterGetIntention = () => {
            return typeWriter('writingGetIntentionTextText', data.getIntentionText, 20).then(() => {
                if (data.AIData.busDirect == '行业' && data.AIData.busOperate !== '检索') {
                    getAICustomizedData();
                } else if (data.AIData.busDirect == '商客') {
                    // ...原有逻辑...
                }
            });
        };
        // ☆☆☆☆☆☆ 找到了方案的 ↓ ☆☆☆☆☆☆
        // 提示用户去右边选择方案
        const typeWriterPromptChooseSolution = () => {
            let text = '';
            getPromptWords({
                solutionName: data.AIData.keyword.solutionList[0].key
            }).then((res) => {
                data.showAnalysisWords = false;
                text = res.data;
                if (data.continueProcess) {
                    typeWriter('writingPromptChooseSolution', text, 20).then(() => {
                        writingOver();
                        emit('changeRightModuleType', 'chooseSolution');
                        emit('getChooseSolutionList', data.showSolutionList);
                    });
                }
            });
        };
        // 打印搜到的方案列表
        const typeWriterSolutionList = async () => {
            if (data.continueProcess) {
                for (const ability of data.showSolutionList) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                        displayReason: '',
                    });
                    data.showShowSolutionList.push(newItem);
                    await processSolutionList(newItem);
                }
            }
        }
        const processSolutionList = async (item) => {
            if (data.continueProcess) {
                await typeWriterForItem(item, 'displayText', '《' + item.name + '》', 20);
                // await typeWriterForItem(item, 'displayReason', item.reason, 20);
            }
        };
        // 通用item打字机
        function typeWriterForItem(item, prop, text, speed = 20) {
            return new Promise((resolve) => {
                // 生成唯一的 itemId
                const itemId = `item_${++itemIdCounter}_${Date.now()}`;

                // 将 item 和 prop 存储到映射表中
                itemMap.set(itemId, { item, prop, resolve });

                // 初始化 item[prop] 为空字符串
                item[prop] = '';

                // 发送任务到 Worker
                typingWorker.postMessage({
                    type: 'addToQueue',
                    item: { itemId, text, speed }
                });
            });
        }
        // 打印引导语1
        const typeWriterGuideWords1 = () => {
            let text = '您可在屏幕右侧';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords1', text, 20).then(() => {
                    typeWriterGuideWords2();
                });
            }
        };
        const typeWriterGuideWords2 = () => {
            let text = '查看方案详情、选择合适的参考的方案';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords2', text, 20).then(() => {
                    typeWriterGuideWords3();
                });
            }
        };
        const typeWriterGuideWords3 = () => {
            let text = '，小麟将参考其为您生成。';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords3', text, 20).then(() => {
                    writingOver();
                    emit('changeRightModuleType', 'chooseSolution');
                    emit('getChooseSolutionList', data.showSolutionList);
                });
            }
        };
        const typeWriterGuideWords4 = () => {
            let text = '若没有合适的参考方案，您也可以点击屏幕右侧右上角的';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords4', text, 20).then(() => {
                    typeWriterGuideWords5();
                });
            }
        };
        const typeWriterGuideWords5 = () => {
            let text = '"AI自主生成"';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords5', text, 20).then(() => {
                    typeWriterGuideWords6();
                });
            }
        };
        const typeWriterGuideWords6 = () => {
            let text = '按钮，小麟会为您生成专属方案。';
            if (data.continueProcess) {
                return typeWriter('writingGuideWords6', text, 20).then(() => {
                    writingOver();
                    emit('changeRightModuleType', 'chooseSolution');
                    emit('getChooseSolutionList', data.showSolutionList);
                });
            }
        };
        const showSolutionDetail = (item) => {
            console.log(item)
        }
        // 已分析用户需求，即将开始xxx方案的生成：
        const typeWriterFirstTitleText = () => {
            if (data.continueProcess) {
                return typeWriter('writingfirstTitleText', data.firstTitleText, 20).then(() => {
                    data.showBr1 = true
                    typeWriterSolutionOverview()
                });
            }
        };
        // 一、方案概述模块打字机
        const typeWriterSolutionOverview = () => {
            if (data.continueProcess) {
                return typeWriter('writingSolutionOverview', data.solutionOverviewText, 20).then(() => {
                    if (data.continueProcess) {
                        typeWriterSolutionOverview2()
                        setTimeout(() => {
                            data.hasGetOverview = true
                            if (data.continueProcess) {
                                typeWriterSolutionOverview3()
                            }
                        }, 1000);
                    }
                });
            }

        };
        // 正在生成方案概述...
        const typeWriterSolutionOverview2 = () => {
            if (data.continueProcess) {
                return typeWriter('writingSolutionOverview2', data.solutionOverviewText2, 20).then(() => {

                });
            }
        };
        // 方案概述PPT已生成
        const typeWriterSolutionOverview3 = () => {
            if (data.continueProcess) {
                return typeWriter('writingSolutionOverview3', data.solutionOverviewText3, 20).then(() => {
                    typeWriterSolutionOverview4()
                });
            }
        };
        // ,PPT内容如下
        const typeWriterSolutionOverview4 = () => {
            if (data.continueProcess) {
                return typeWriter('writingSolutionOverview4', data.solutionOverviewText4, 20).then(() => {
                    typeWriterSolutionOverview5()
                });
            }

        };
        // 方案概述正文
        const typeWriterSolutionOverview5 = () => {
            if (data.continueProcess) {
                return typeWriter('writingSolutionOverview5', data.solutionOverviewText5, 20).then(() => {
                    typeWriterApplicationScene()
                });
            }
        };
        // 二、应用场景
        const typeWriterApplicationScene = () => {
            if (data.continueProcess) {
                emit('addPPTBigTitle', '方案场景')
                data.goHistoryData.PPTList.push({
                    bigTitle: '方案场景'
                })
                data.applicationSceneText = '二、应用场景'
                data.showBr2 = true
                return typeWriter('writingApplicationSceneText', data.applicationSceneText, 20).then(() => {
                    typeWriterApplicationScene2()
                });
            }

        };
        // （场景顺序逻辑待优化）现在没有实际文字打印
        const typeWriterApplicationScene2 = () => {
            if (data.continueProcess) {
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.applicationSceneIndex2 < data.applicationSceneText2.length) {
                            data.writingApplicationSceneText2 += data.applicationSceneText2.charAt(data.applicationSceneIndex2);
                            data.applicationSceneIndex2++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                if (data.thinkingStatus) {
                                    // typeWriterApplicationScene3()
                                    writingThinkingWords()
                                }
                                getAIElementPoints()
                            }
                        }
                    };
                    write();
                });
            }

        };
        // 小麟正在思考您的xx方案可能由哪些应用场景组成...
        const typeWriterApplicationScene3 = () => {
            if (data.continueProcess) {
                data.applicationSceneText3 = '小麟正在思考您的' + data.AIData.keyword.solutionList[0].key + '方案可能由哪些应用场景组成...'
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.applicationSceneIndex3 < data.applicationSceneText3.length) {
                            data.writingApplicationSceneText3 += data.applicationSceneText3.charAt(data.applicationSceneIndex3);
                            data.applicationSceneIndex3++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                setTimeout(() => {
                                    // typeWriterWaitText()
                                    writingThinkingWords()
                                }, 4000);
                            }
                        }
                    };
                    write();
                });
            }
        };
        // 新加的，因要素思考时间过长，打一些提示用户稍等的文字
        const typeWriterWaitText = async () => {
            let waitArr = [{
                text: '小麟思考的内容较多，请您耐心等待~',
            }, {
                text: '小麟正在起草内容了，写完就会显示给您看哦~',
            }, {
                text: '小麟快写完了，麻烦您再等一下哦~',
            }]
            if (data.continueProcess) {
                for (const ability of waitArr) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                    });
                    if (data.continueProcess) {
                        data.showWaitTextList.push(newItem);
                        await processWritingWaitText(newItem);
                        // 每5秒钟后打印下一句
                        await new Promise(resolve => setTimeout(resolve, 6000));
                    }
                }
            }
        }
        // 打一些提示用户稍等的文字
        const processWritingWaitText = async (item) => {
            if (data.continueProcess) {
                await typeWrite(item, 'displayText', item.text);
            }
        }
        // 调要素思考接口
        const getAIElementPoints = () => {
            if (data.continueProcess) {
                let arr = []
                for (let i of data.peopleSaidAbilityList) {
                    arr.push(i.key)
                }
                console.log('11111111111111111', data.peopleSaidAbilityList)
                // AI要素思考接口给的能力
                getElementExpand({
                    name: data.AIData.keyword.solutionList[0].key,
                    elements: arr,
                    desc: data.nowSolutionDescription,
                    // req: data.AIData.keyword.solutionList[0].key
                }).then((res) => {
                    if (res.data.elementList.length !== 0) {
                        for (let i of res.data.elementList) {
                            data.goSearchAbilityList.push(i)
                        }
                    }
                    data.goHistoryData.goSearchAbilityList = data.goSearchAbilityList
                    console.log('222222222222222222222', data.goSearchAbilityList)
                    data.hasGetElementPoint = true

                    // 等待data.thinkingOver变为true
                    const waitForThinkingOver = () => {
                        if (data.thinkingOver) {
                            // data.thinkingOver为true时执行后续方法
                            closeStreamThinkingource()
                            if (data.continueProcess) {
                                typeWriterApplicationScene4()
                            }
                        } else {
                            // 如果还是false，继续等待
                            setTimeout(waitForThinkingOver, 100)
                        }
                    }

                    // 开始等待
                    waitForThinkingOver()
                })

            }

        }
        // 思考完成，小麟即将为您的xx方案定制以下应用场景PPT：
        const typeWriterApplicationScene4 = () => {
            data.applicationSceneText4 = '思考完成，小麟即将为您的' + data.AIData.keyword.solutionList[0].key + '方案定制以下应用场景PPT：'
            if (data.continueProcess) {
                return typeWriter('writingApplicationSceneText4', data.applicationSceneText4, 20).then(() => {
                    getElementRetrial()
                });
            }

        };

        // 思考完成先打印方案自带的场景（假装接口拿的）
        const writingSolutionCaseList = async () => {
            console.log('方案自带场景☆☆☆☆', data.solutionSceneList)
            if (data.continueProcess) {
                for (const ability of data.solutionSceneList) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                        displayDescription: '',
                        id: ability.id,
                        specialTitle: '场景',
                        slideUrls: ability.slideUrls,
                        title: ability.name,
                        isNew: true
                    });
                    data.showSolutionSceneList.push(newItem);
                    data.abilityNamesList.push(newItem.name)
                    await processSolutionSceneList(newItem);
                }
            }
            if (data.goSearchAbilityList.length !== 0) {
                if (data.continueProcess) {
                    // await writingThinkAbilityList()
                    await getElementRetrial()
                }
                // 改成流式以后下面的东西在流式里面写
                // if (data.continueProcess) {
                //     if (data.noSearchThinkAbilityList.length !== 0) {
                //         await typeWritingNoSearchAbility()
                //     } else {
                //         await typeWriterApplicationCase()
                //     }
                // }
            } else {
                for (let i of data.showSolutionSceneList) {
                    data.goHistoryData.abilityList.push(
                        {
                            name: i.displayText,
                            description: i.displayDescription,
                            id: i.id,
                            specialTitle: i.specialTitle,
                            isSolutionOwn: true,
                            title: i.displayText,
                            slideUrls: i.slideUrls,
                            isNew: true,
                        }
                    )
                }
                if (data.continueProcess) {
                    await typeWriterApplicationCase()
                }
            }
            // if (data.continueProcess) {
            // }
        }
        const processSolutionSceneList = async (item) => {
            if (data.continueProcess) {
                if (item.slideUrls.length !== 0) {
                    emit('addPPTSmallTitle', item.name)
                    if (item.slideUrls.length !== 0) {
                        data.goHistoryData.PPTList.push({
                            smallTitle: item.name
                        })
                    }
                    let arr = []
                    for (let i of item.slideUrls) {
                        arr.push({
                            img: i
                        })
                    }
                    emit('addPPT', arr)
                    for (let i of item.slideUrls) {
                        data.goHistoryData.PPTList.push({
                            img: i
                        })
                    }
                }
                await typeWrite(item, 'displayText', item.name);
            }
            if (data.continueProcess) {
                await typeWrite(item, 'displayDescription', '：' + item.description);
            }
        };

        // 检查并执行后续逻辑
        const checkAndRunNextStep = () => {
            if (
                data.isSourceClosed &&
                data.messageQueue.length === 0 &&
                !data.isProcessingQueue &&
                data.continueProcess
            ) {
                if (data.noSearchThinkAbilityList.length !== 0) {
                    typeWritingNoSearchAbility();
                } else {
                    typeWriterApplicationCase();
                }
            }
        };

        // 流式处理消息队列
        const processMessageQueue = async () => {
            if (data.isProcessingQueue) return;
            data.isProcessingQueue = true;
            while (data.messageQueue.length > 0 && data.continueProcess) {
                const eventData = data.messageQueue.shift();
                if (eventData) {
                    const newItem = reactive({
                        displayRealTitle: '',
                        displayIntroduction: '',
                        title: eventData.detail.name,
                        description: eventData.detail.descShow,
                        searched: '已搜到',
                        isNew: true,
                        id: eventData.detail.id,
                        detail: eventData.detail,
                        specialTitle: eventData.detail.specialTitle,
                    });
                    data.newShowSceneList.push(newItem);
                    // 等待打字机效果执行完毕
                    await newProcessAddAbility(newItem);
                }
            }
            data.isProcessingQueue = false;
            checkAndRunNextStep(); // 处理完毕后检查
        };

        // 用流式的召回并发
        const getElementRetrial = async () => {
            data.isSourceClosed = false; // 新增
            let str = data.goSearchAbilityList.map(item => item.key).join(",");
            if (data.continueProcess) {
                // 获取token，可以从localStorage、sessionStorage或者vuex store中获取
                let token = localStorage.getItem('token') || sessionStorage.getItem('token') || ''
                let url = baseURL + 'view/streamChat/elementRetrial?elements=' + str + '&solutionId=' + data.nowSolutionId + '&token=' + token

                if (data.streamElementRetrialSource) {
                    data.streamElementRetrialSource.close()
                }
                const source = new EventSource(url);
                data.streamElementRetrialSource = source
                source.onmessage = function (event) {
                    console.log('连接已建立')
                    console.log('JSON.parse(event.data)', JSON.parse(event.data))
                    const parsed = JSON.parse(event.data);
                    parsed.detail.title = parsed.detail.name
                    if (parsed.specialTitle == '能力') {
                        parsed.detail.specialTitle = '能力'
                        parsed.detail.customizedType = 2
                    } else if (parsed.specialTitle == '场景') {
                        parsed.detail.specialTitle = '场景'
                        parsed.detail.customizedType = 3
                    } else if (parsed.specialTitle == '产品') {
                        parsed.detail.specialTitle = '产品'
                        parsed.detail.customizedType = 4
                    }
                    if (data.continueProcess && parsed) {
                        data.messageQueue.push(parsed);
                        processMessageQueue();
                    }
                }
                source.onerror = function (err) {
                    if (data.streamElementRetrialSource && data.streamElementRetrialSource.readyState === EventSource.CLOSED) {
                        console.log('连接已由用户关闭')
                        return
                    }
                    console.log('连接已关闭,走接下来的路线')
                    source.close()
                    data.streamElementRetrialSource = null
                    data.isSourceClosed = true;
                    checkAndRunNextStep();
                }
            }
        }

        const newProcessAddAbility = async (item) => {
            console.log('itemzdlzdlzdl', item)
            if (data.continueProcess) {
                if (item.detail.slideUrls && item.detail.slideUrls.length !== 0) {
                    emit('addPPTSmallTitle', item.detail.name)
                    data.goHistoryData.PPTList.push({
                        smallTitle: item.detail.name
                    })
                    let arr = []
                    for (let i of item.detail.slideUrls) {
                        arr.push({
                            img: i
                        })
                        data.goHistoryData.PPTList.push({
                            img: i
                        })
                    }
                    emit('addPPT', arr)
                }
                data.goCustomizedList.push(item.detail)
                data.goHistoryData.abilityList.push({
                    name: item.title,
                    description: item.description,
                    id: item.id,
                    specialTitle: item.specialTitle,
                    title: item.title,
                    slideUrls: item.detail.slideUrls,
                    isNew: true,
                })
                await typeWriterForItem(item, 'displayRealTitle', item.title, 20);
                await typeWriterForItem(item, 'displayIntroduction', '：' + item.description, 20);
            }

        }
        // 打印完方案自带的场景，拿AI思考的要素去调接口（场景由流式返回以后已弃用）
        const writingThinkAbilityList = async () => {
            // 把方案自带的场景的key和description存到goHistoryData
            if (data.hasSolution) {
                for (let i of data.showSolutionSceneList) {
                    data.goHistoryData.abilityList.push(
                        {
                            name: i.displayText,
                            description: i.displayDescription,
                            id: i.id,
                            specialTitle: i.specialTitle,
                            isSolutionOwn: true,
                            title: i.title,
                            slideUrls: i.slideUrls,
                            isNew: true,
                        }
                    )
                }
            }
            // console.log('data.abilityNamesList', data.abilityNamesList)
            for (const ability of data.goSearchAbilityList) {
                const newItem = reactive({
                    ...ability,
                    displayRealTitle: '',
                    displayIntroduction: '',
                    isNew: true,
                });
                data.showThinkAbilityList.push(newItem);
                if (data.continueProcess) {
                    await processAddAbility(newItem);
                }
            }
        }
        const processAddAbility = async (item) => {
            let compareSimilarityList = [] //匹配度集合的列表
            let highestMatch = {}//最高匹配度的项
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 2,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查能力', res)
                    // if (res.data.dataList.length !== 0) {
                    //     res.data.dataList[0].similarity = res.data.priority[0].similarity
                    //     res.data.dataList[0].specialTitle = '能力'
                    //     res.data.dataList[0].customizedType = 2
                    //     compareSimilarityList.push(res.data.dataList[0])
                    // }
                    if (res.data.dataList.length !== 0) {
                        // 找到第一个slideUrls数组长度不为0的项的索引
                        let targetIndex = -1;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetIndex = i;
                                break;
                            }
                        }

                        // 如果找到了符合条件的项，给它赋值
                        if (targetIndex !== -1) {
                            res.data.dataList[targetIndex].similarity = res.data.priority[targetIndex].similarity;
                            res.data.dataList[targetIndex].specialTitle = '能力';
                            res.data.dataList[targetIndex].customizedType = 2;
                            compareSimilarityList.push(res.data.dataList[targetIndex]);
                        }
                    }
                })
            }
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 3,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查场景', res)
                    if (res.data.dataList.length !== 0) {
                        // res.data.dataList[0].similarity = res.data.priority[0].similarity
                        // res.data.dataList[0].specialTitle = '场景'
                        // res.data.dataList[0].customizedType = 3
                        // compareSimilarityList.push(res.data.dataList[0])
                        // 找到第一个isSensitive字段为0的项
                        let targetItem = null;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].isSensitive === 0 && res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetItem = res.data.dataList[i];
                                break;
                            }
                        }

                        // 如果找到了符合条件的项，给它赋值
                        if (targetItem) {
                            targetItem.similarity = res.data.priority[0].similarity;
                            targetItem.specialTitle = '场景';
                            targetItem.customizedType = 3;
                            compareSimilarityList.push(targetItem);
                        }
                    }
                })
            }
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 5,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查产品', res)
                    if (res.data.dataList.length !== 0) {
                        // res.data.dataList[0].similarity = res.data.priority[0].similarity
                        // res.data.dataList[0].specialTitle = '产品'
                        // res.data.dataList[0].customizedType = 4
                        // compareSimilarityList.push(res.data.dataList[0])
                        let targetIndex = -1;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetIndex = i;
                                break;
                            }
                        }
                        // 如果找到了符合条件的项，给它赋值
                        if (targetIndex !== -1) {
                            res.data.dataList[targetIndex].similarity = res.data.priority[targetIndex].similarity;
                            res.data.dataList[targetIndex].specialTitle = '产品';
                            res.data.dataList[targetIndex].customizedType = 4;
                            compareSimilarityList.push(res.data.dataList[targetIndex]);
                        }
                    }
                    // console.log('compareSimilarityList', compareSimilarityList)
                    // 获取符合要求的能力、场景、产品里匹配度最高的一项
                    if (compareSimilarityList.length !== 0) {
                        highestMatch = compareSimilarityList.reduce(
                            (max, item) => (item.similarity > max.similarity ? item : max),
                            compareSimilarityList[0]
                        )
                    }
                    // 把匹配度最高的加入准备去定制的数组
                    console.log('匹配度最高的一项', highestMatch)
                    if (highestMatch.slideUrls && highestMatch.slideUrls.length !== 0) {

                    }
                    data.goCustomizedList.push(highestMatch)
                    // console.log('最终去定制的数组', data.goCustomizedList)
                })
            }

            await new Promise(resolve => setTimeout(() => {
                // 模拟循环调接口有没有拿到合适的能力，拿item.key和item.description
                // 搜没搜到能力
                if (compareSimilarityList.length !== 0) {
                    item.title = highestMatch.name
                    item.specialTitle = highestMatch.specialTitle
                    if (highestMatch.specialTitle == '能力') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    } else if (highestMatch.specialTitle == '场景') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    } else if (highestMatch.specialTitle == '产品') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    }
                    item.searched = '已搜到'
                } else {
                    item.title = null
                    item.description = '很抱歉，平台素材有限，未能为您定制合适的【' + item.key + '】能力'
                    item.searched = '未搜到'
                    data.noSearchThinkAbilityList.push({
                        name: item.key,
                        reason: item.reason,
                        fromUser: item.fromUser,
                    })
                }
                // console.log('没搜到的集合', data.noSearchThinkAbilityList)
                resolve();
            }, 500));
            if (compareSimilarityList.length !== 0 && data.continueProcess) {
                // 已搜到
                if (!data.abilityNamesList.includes(highestMatch.name)) {
                    // 3.0暂时不放缩略图
                    if (data.nowSolutionId) {
                        if (highestMatch.slideUrls && highestMatch.slideUrls.length !== 0) {
                            // PPT的
                            emit('addPPTSmallTitle', highestMatch.name)
                            if (highestMatch.slideUrls.length !== 0) {
                                data.goHistoryData.PPTList.push({
                                    smallTitle: highestMatch.name
                                })
                            }

                            let arr = []
                            for (let i of highestMatch.slideUrls) {
                                arr.push({
                                    img: i
                                })
                            }
                            emit('addPPT', arr)
                            for (let i of highestMatch.slideUrls) {
                                data.goHistoryData.PPTList.push({
                                    img: i
                                })
                            }
                        }
                    }

                    data.abilityNamesList.push(highestMatch.name)
                    // console.log('data.abilityNamesList', data.abilityNamesList)
                    if (item.fromUser) {
                        closeStreamThinkingource()
                        // console.log('item.key', item.key, 'item.key.includes("【"', item.key.includes("【"), 'includes')
                        await typeWrite(item, 'displayRealTitle', item.title + '【' + item.key + '】');
                        await typeWrite(item, 'displayIntroduction', '：' + item.description);
                    } else {
                        closeStreamThinkingource()
                        // console.log('item.key', item.key, 'item.key.includes("【"', item.key.includes("【"), 'includesNo')
                        await typeWrite(item, 'displayRealTitle', item.title);
                        await typeWrite(item, 'displayIntroduction', '：' + item.description);
                    }
                } else {
                    data.showThinkAbilityList.splice(data.showThinkAbilityList.length - 1, 1)
                }
            } else if (compareSimilarityList.length == 0) {
                // 没搜到
                // await typeWrite(item, 'displayRealTitle', item.key);
                // await typeWrite(item, 'displayIntroduction', '：' + item.description);
                data.showThinkAbilityList.splice(data.showThinkAbilityList.length - 1, 1)
            }
        }
        // 没有搜到能力（场景由流式返回以后，不会出现搜不到的情况）
        const typeWritingNoSearchAbility = () => {
            if (data.continueProcess) {
                return typeWriter('writingNoSearchAbilityText', data.noSearchAbilityText, 20).then(() => {
                    if (data.continueProcess) {
                        typeWritingNoSearchAbilityList().then(() => {
                            typeWriterApplicationCase()
                        })
                    }
                });
            }
        }
        // 展示没有搜到的能力的数组（场景由流式返回以后，不会出现搜不到的情况）
        const typeWritingNoSearchAbilityList = async () => {
            if (data.continueProcess) {
                for (const ability of data.noSearchThinkAbilityList) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                        displayReason: '',
                    });
                    data.showNoSearchAbilityList.push(newItem);
                    await processNoSearchAbility(newItem);
                }
            }
        }
        const processNoSearchAbility = async (item) => {
            if (data.continueProcess) {
                // if (item.fromUser) {
                //     await typeWrite(item, 'displayText', item.name);
                // } else {
                //     await typeWrite(item, 'displayText', item.name + '：');
                //     await typeWrite(item, 'displayReason', item.reason);
                // }
                await typeWriterForItem(item, 'displayText', item.name);
            }
        };

        // 三、应用案例
        const typeWriterApplicationCase = () => {
            // 把用户说的能力存到goHistoryData
            for (let i of data.showThinkAbilityList) {
                data.goHistoryData.abilityList.push({
                    name: i.displayRealTitle,
                    description: i.displayIntroduction,
                    id: i.id,
                    specialTitle: i.specialTitle,
                    isSolutionOwn: false,
                    title: i.title,
                    slideUrls: i.slideUrls,
                    isNew: true,
                })
            }
            // 如果有没搜到的能力，也存到goHistoryData
            if (data.noSearchThinkAbilityList.length !== 0) {
                for (let i of data.noSearchThinkAbilityList) {
                    data.goHistoryData.noSearchedAbilityList.push({
                        name: i.name,
                        reason: i.reason,
                        fromUser: i.fromUser,
                        id: i.id
                    })
                }
            }
            if (data.continueProcess) {
                emit('addPPTBigTitle', '方案案例')
                data.goHistoryData.PPTList.push({
                    bigTitle: '方案案例'
                })
                data.showBr3 = true
                return typeWriter('writingApplicationCaseText', data.applicationCaseText, 20).then(() => {
                    if (data.continueProcess) {
                        if (data.AIData.keyword.caseList && data.AIData.keyword.caseList.length !== 0) {
                            // typeWriterPeopleCases()
                            searchUserCase()
                        } else {
                            typeWriterApplicationCase2()
                        }
                    }
                });
            }

        };
        // 先搜用户说的案例，看有没有和方案自带的案例相同的
        const searchUserCase = async () => {
            if (data.continueProcess) {
                const promises = data.AIData.keyword.caseList.map(i => {
                    return getRetrieveList({
                        knowledgeType: 6,
                        description: i.description,
                        topK: 10,
                        name: i.key,
                        // solutionId: data.nowSolutionId,
                        aim: data.AIData.aim
                    }).then((res) => {
                        console.log('查案例', res)
                        if (res.data.dataList.length !== 0) {
                            res.data.dataList[0].specialTitle = '案例'
                            res.data.dataList[0].customizedType = 6
                            res.data.dataList[0].searched = '已搜到'
                            res.data.dataList[0].searchKey = i.key
                            // 改案例的优先级
                            // 检查是否与data.solutionCaseList中的案例重复
                            // const isDuplicate = data.solutionCaseList.some(solutionCase =>
                            //     solutionCase.name === res.data.dataList[0].caseName
                            // );
                            // // 如果不重复，则添加到data.userCaseList中
                            // if (!isDuplicate) {
                            //     data.userCaseList.push(res.data.dataList[0]);
                            //     data.goCustomizedList.push(res.data.dataList[0])
                            // }
                            data.userCaseList.push(res.data.dataList[0]);
                            data.goCustomizedList.push(res.data.dataList[0])
                        } else {
                            data.userCaseList.push({
                                searchKey: i.key,
                                caseName: '',
                                searched: '未搜到',
                                descShow: '很抱歉，平台素材有限，未能为您定制合适的' + i.key + '案例'
                            })
                        }
                    })
                });

                // 等待所有接口调用完成
                await Promise.all(promises);

                // 判断data.userCaseList数组的length
                if (data.userCaseList.length !== 0) {
                    console.log('data.userCaseList☆:', data.userCaseList);
                    typeWriterPeopleCases();
                } else {
                    typeWriterApplicationCase2();
                }
            }
        }


        // 打印用户提到的案例
        const typeWriterPeopleCases = () => {
            if (data.continueProcess) {
                let arr = []
                for (let i of data.AIData.keyword.caseList) {
                    i.name = '【' + i.key + '】'
                    arr.push(i.name)
                }
                data.peopleCasesText = '小麟正在尝试为您定制您所需要的案例...'
                data.goHistoryData.peopleCaseWords = data.peopleCasesText
                return typeWriter('writingPeopleCasasText', data.peopleCasesText, 20).then(async () => {
                    if (data.continueProcess) {
                        await writingPeopleCasesList()
                    }
                    if (data.continueProcess) {
                        await typeWriterApplicationCase2()
                    }
                });
            }
        }
        // 依次搜用户提到的案例
        const writingPeopleCasesList = async () => {
            for (const ability of data.userCaseList) {
                const newItem = reactive({
                    ...ability,
                    displayText: '',
                    displayRealTitle: '',
                    displayIntroduction: '',
                    isNew: true,
                });
                if (data.continueProcess) {
                    data.showPeopleCasesList.push(newItem);
                    await processAddPeopleCase(newItem);
                }

            }
        }
        const processAddPeopleCase = async (item) => {
            if (data.continueProcess) {
                if (item.searched == '已搜到') {
                    if (item.slideUrls && item.slideUrls.length !== 0) {
                        // PPT的
                        emit('addPPTSmallTitle', item.caseName)
                        if (item.slideUrls.length !== 0) {
                            data.goHistoryData.PPTList.push({
                                smallTitle: item.caseName
                            })
                        }

                        let arr = []
                        for (let i of item.slideUrls) {
                            arr.push({
                                img: i
                            })
                        }
                        emit('addPPT', arr)
                        for (let i of item.slideUrls) {
                            data.goHistoryData.PPTList.push({
                                img: i
                            })
                        }
                    }
                    // item.id = item.id
                    // item.slideUrls = item.slideUrls
                    item.title = item.caseName
                    await typeWriterForItem(item, 'displayText', item.searchKey);
                    await typeWriterForItem(item, 'displayRealTitle', '《' + item.caseName + '》：');
                    await typeWriterForItem(item, 'displayIntroduction', item.descShow);
                } else if (item.searched == '未搜到') {
                    // console.log('没有搜到案例')
                    await typeWriterForItem(item, 'displayText', item.searchKey + '：');
                    await typeWriterForItem(item, 'displayIntroduction', item.descShow);
                }
            }
        }

        // const processAddPeopleCase = async (item) => {
        //     let dataList = []
        //     if (data.continueProcess) {
        //         await getRetrieveList({
        //             knowledgeType: 6,
        //             description: item.description,
        //             topK: 10,
        //             name: item.key,
        //             // solutionId: data.nowSolutionId,
        //             aim: data.AIData.aim
        //         }).then((res) => {
        //             // console.log('查案例', res)
        //             dataList = res.data.dataList
        //             if (res.data.dataList.length !== 0) {
        //                 res.data.dataList[0].specialTitle = '案例'
        //                 res.data.dataList[0].customizedType = 6
        //                 data.goCustomizedList.push(res.data.dataList[0])

        //             }
        //             // console.log('最终去定制的数组', data.goCustomizedList)
        //         })
        //     }

        //     await new Promise(resolve => setTimeout(() => {
        //         // 搜没搜到案例
        //         if (dataList.length !== 0) {
        //             item.searched = '已搜到'
        //         } else {
        //             item.searched = '未搜到'
        //             item.unSearched = '很抱歉，平台素材有限，未能为您定制合适的' + item.name + '案例'
        //         }
        //         resolve();
        //     }, 500));
        //     if (data.continueProcess) {
        //         if (dataList.length !== 0) {
        //             if (dataList[0].slideUrls && dataList[0].slideUrls.length !== 0) {
        //                 // PPT的
        //                 emit('addPPTSmallTitle', dataList[0].caseName)
        //                 if (dataList[0].slideUrls.length !== 0) {
        //                     data.goHistoryData.PPTList.push({
        //                         smallTitle: dataList[0].caseName
        //                     })
        //                 }

        //                 let arr = []
        //                 for (let i of dataList[0].slideUrls) {
        //                     arr.push({
        //                         img: i
        //                     })
        //                 }
        //                 emit('addPPT', arr)
        //                 for (let i of dataList[0].slideUrls) {
        //                     data.goHistoryData.PPTList.push({
        //                         img: i
        //                     })
        //                 }
        //             }
        //             item.id = dataList[0].id
        //             item.slideUrls = dataList[0].slideUrls
        //             item.title = dataList[0].caseName
        //             await typeWrite(item, 'displayText', item.name);
        //             // await typeWrite(item, 'displayArrow', '：===>');
        //             await typeWrite(item, 'displayRealTitle', '《' + dataList[0].caseName + '》：');
        //             await typeWrite(item, 'displayIntroduction', dataList[0].descShow);
        //         } else if (dataList.length == 0) {
        //             // console.log('没有搜到案例')
        //             await typeWrite(item, 'displayText', item.name);
        //             // await typeWrite(item, 'displayArrow', '：===>');
        //             await typeWrite(item, 'displayIntroduction', item.unSearched);
        //         }
        //     }
        // }

        // 小麟发现以下案例很适合您的方案，将为您加入定制：
        const typeWriterApplicationCase2 = () => {
            // 把用户说的案例存到goHistoryData
            if (data.AIData.keyword.caseList && data.AIData.keyword.caseList.length !== 0) {
                for (let i of data.showPeopleCasesList) {
                    data.goHistoryData.peopleCaseList.push({
                        searched: i.searched,
                        peopleWords: i.displayText,
                        name: i.displayRealTitle,
                        description: i.displayIntroduction,
                        slideUrls: i.slideUrls,
                        specialTitle: i.specialTitle,
                        id: i.id,
                        title: i.title,
                        isNew: true,
                    })
                }
            }
            if (data.continueProcess) {
                if (data.AIData.keyword.caseList && data.AIData.keyword.caseList.length !== 0) {
                    data.showBr5 = true
                }
                return typeWriter('writingApplicationCaseText2', data.applicationCaseText2, 20).then(async () => {
                    await new Promise(r => setTimeout(r, 1000));//一秒之后打印自带的方案（假装是获取的）
                    if (data.continueProcess) {
                        await typeWriterSolutionCase()
                        // for (let i of data.nowSolutionDetail.caseInfoList) {
                        //     if (i.slideUrls.length !== 0) {
                        //         // PPT的
                        //         for (let j of i.slideUrls) {
                        //             data.goHistoryData.PPTList.push({
                        //                 img: j
                        //             })
                        //         }
                        //     }
                        // }
                    }
                    if (data.continueProcess) {
                        await typeWriterSummarize()
                    }
                });
            }

        }
        // 把方案里自带的案例加进来
        const typeWriterSolutionCase = async () => {
            // 改案例的优先级
            let arr = []
            for (let i of data.solutionCaseList) {
                const isDuplicate = data.userCaseList.some(solutionCase =>
                    solutionCase.caseName === i.name
                );
                if (!isDuplicate) {
                    arr.push(i)
                }
            }
            console.log('222xxxxxxxx', arr)
            if (data.nowSolutionDetail.caseInfoList.length !== 0) {
                for (let i of data.nowSolutionDetail.caseInfoList) {
                    data.goCustomizedList.push(i)
                }
            }

            // console.log('solutionCaseList★slideUrls', data.solutionCaseList)
            for (const ability of arr) {
                const newItem = reactive({
                    ...ability,
                    displayText: '',
                    displayDescription: '',
                    title: ability.name,
                    isNew: true,
                });
                if (data.continueProcess) {
                    data.showSolutionCaseList.push(newItem);
                    // 把方案自带的案例存到historyShowAIData
                    data.goHistoryData.solutionCaseList.push(newItem)
                    await processAddSolutionCase(newItem);
                }

            }
        }
        const processAddSolutionCase = async (item) => {
            if (item.slideUrls.length !== 0) {
                emit('addPPTSmallTitle', item.name)
                data.goHistoryData.PPTList.push({
                    smallTitle: item.name
                })
            }

            let arr = []
            for (let i of item.slideUrls) {
                arr.push({
                    img: i
                })
                data.goHistoryData.PPTList.push({
                    img: i
                })
            }
            emit('addPPT', arr)
            await typeWriterForItem(item, 'displayText', item.name);
            await typeWriterForItem(item, 'displayDescription', '：' + item.description);
        }

        // ☆☆☆☆☆☆ 没找到方案的 ↓ ☆☆☆☆☆☆

        // 很抱歉，小麟未能找到与您方案相符的基础信息
        const writingNoSolutionWords = () => {
            if (data.continueProcess) {
                return typeWriter('writingNoSolutionText', data.noSolutionText, 20).then(() => {
                    data.showNoSolutionContinue = true
                    writingOver()
                });
            }

        }
        // 没搜到方案是否继续按钮
        const noSolutionContinue = (type) => {
            if (type == '1') {
                // 继续生成
                data.showNoSolutionAnalysisWords = false
                data.showNoSolutionContinue = false
                data.isAIWriting = true
                emit("continueScroll")
                noSolutionCustomized()

            } else if (type == '2') {
                // 不继续，结束
                writingOver()
                emit('clearAISession')
            }
        }
        // 没搜到方案但是继续生成(好的，小麟认为您的方案中可能包含以下内容：)
        const noSolutionCustomized = () => {
            data.showBr6 = true
            return typeWriter('writingNoSolutionCustomized', data.noSolutionCustomizedText, 20).then(() => {
                data.showThinkingBox = true
                // setTimeout(() => {
                //     typeNoSolutionWriterWaitText()
                // }, 4000);
                writingThinkingWords()
                let arr = []
                for (let i of data.peopleSaidAbilityList) {
                    arr.push(i.key)
                }
                getElementExpand({
                    // req: data.AIData.keyword.solutionList[0].key
                    name: data.AIData.keyword.solutionList[0].key,
                    elements: arr,
                    desc: data.nowSolutionDescription,
                }).then((res) => {
                    data.isThinkingOver = true
                    data.isShowNoSolutionWaitText = false
                    if (res.data.elementList.length !== 0) {
                        for (let i of res.data.elementList) {
                            data.goSearchAbilityList.push(i)
                        }
                    }

                    // 等待data.thinkingOver变为true
                    const waitForThinkingOver = () => {
                        if (data.thinkingOver) {
                            // data.thinkingOver为true时执行后续方法
                            if (data.continueProcess) {
                                noSolutionCustomized2()
                            }
                        } else {
                            // 如果还是false，继续等待
                            setTimeout(waitForThinkingOver, 100)
                        }
                    }

                    // 开始等待
                    waitForThinkingOver()
                })
            });
        }
        const typeNoSolutionWriterWaitText = async () => {
            let waitArr = [{
                text: '小麟思考内容较多，请您耐心等待~',
            }, {
                text: '小麟正在起草内容了，写完就会显示给您看哦~',
            }, {
                text: '小麟快写完了，麻烦您再等一下哦~',
            }]
            if (data.continueProcess) {
                for (const ability of waitArr) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                    });
                    if (data.continueProcess) {
                        data.showNoSolutionWaitTextList.push(newItem);
                        await processNoSolutionWritingWaitText(newItem);
                        // 每5秒钟后打印下一句
                        await new Promise(resolve => setTimeout(resolve, 10000));
                    }
                }
            }
        }
        // 打一些提示用户稍等的文字
        const processNoSolutionWritingWaitText = async (item) => {
            if (data.continueProcess) {
                await typeWrite(item, 'displayText', item.text);
            }
        }
        // 一、应用场景
        const noSolutionCustomized2 = () => {
            emit('addPPTBigTitle', '方案场景')
            data.goHistoryData.PPTList.push({
                bigTitle: '方案场景'
            })
            data.showBr6 = true
            return typeWriter('writingNoSolutionCustomized2', data.noSolutionCustomizedText2, 20).then(async () => {
                if (data.continueProcess) {
                    await noSolutionWritingThinkAbilityList()
                }
                if (data.continueProcess) {
                    if (data.AIData.keyword.caseList && data.AIData.keyword.caseList.length !== 0) {
                        // 如果提到了案例
                        emit('addPPTBigTitle', '方案案例')
                        data.goHistoryData.PPTList.push({
                            bigTitle: '方案案例'
                        })
                        await noSolutionCustomized3()
                    } else {
                        // 没提到案例直接结束
                        await typeWriterSummarize()
                    }
                }
            });
        }
        // 没找到方案的时候，调接口搜要素
        const noSolutionWritingThinkAbilityList = async () => {
            for (const ability of data.goSearchAbilityList) {
                const newItem = reactive({
                    ...ability,
                    displayKey: '',
                    displayRealTitle: '',
                    displayIntroduction: '',
                    isNew: true,
                });
                // console.log('--------------', newItem)
                if (data.continueProcess) {
                    data.showNoSolutionThinkAbilityList.push(newItem);
                    await noSolutionProcessAddAbility(newItem);
                }
            }
        }
        const noSolutionProcessAddAbility = async (item) => {
            // console.log('2222222222222222')
            let compareSimilarityList = [] //匹配度集合的列表
            let highestMatch = {}//最高匹配度的项
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 2,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查能力', res)
                    // if (res.data.dataList.length !== 0) {
                    //     res.data.dataList[0].similarity = res.data.priority[0].similarity
                    //     res.data.dataList[0].specialTitle = '能力'
                    //     res.data.dataList[0].customizedType = 2
                    //     compareSimilarityList.push(res.data.dataList[0])
                    // }
                    if (res.data.dataList.length !== 0) {
                        // 找到第一个slideUrls数组长度不为0的项的索引
                        let targetIndex = -1;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetIndex = i;
                                break;
                            }
                        }

                        // 如果找到了符合条件的项，给它赋值
                        if (targetIndex !== -1) {
                            res.data.dataList[targetIndex].similarity = res.data.priority[targetIndex].similarity;
                            res.data.dataList[targetIndex].specialTitle = '能力';
                            res.data.dataList[targetIndex].customizedType = 2;
                            compareSimilarityList.push(res.data.dataList[targetIndex]);
                        }
                    }
                })
            }
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 3,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查场景', res)
                    if (res.data.dataList.length !== 0) {
                        // res.data.dataList[0].similarity = res.data.priority[0].similarity
                        // res.data.dataList[0].specialTitle = '场景'
                        // res.data.dataList[0].customizedType = 3
                        // compareSimilarityList.push(res.data.dataList[0])
                        // 找到第一个isSensitive字段为0的项
                        let targetItem = null;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].isSensitive === 0 && res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetItem = res.data.dataList[i];
                                break;
                            }
                        }

                        // 如果找到了符合条件的项，给它赋值
                        if (targetItem) {
                            targetItem.similarity = res.data.priority[0].similarity;
                            targetItem.specialTitle = '场景';
                            targetItem.customizedType = 3;
                            compareSimilarityList.push(targetItem);
                        }
                    }
                })
            }
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 5,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    type: 1,
                    solutionId: data.nowSolutionId
                }).then((res) => {
                    // console.log('查产品', res)
                    if (res.data.dataList.length !== 0) {
                        // res.data.dataList[0].similarity = res.data.priority[0].similarity
                        // res.data.dataList[0].specialTitle = '产品'
                        // res.data.dataList[0].customizedType = 4
                        // compareSimilarityList.push(res.data.dataList[0])
                        // 找到第一个slideUrls数组长度不为0的项的索引
                        let targetIndex = -1;
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].slideUrls && res.data.dataList[i].slideUrls.length > 0) {
                                targetIndex = i;
                                break;
                            }
                        }
                        // 如果找到了符合条件的项，给它赋值
                        if (targetIndex !== -1) {
                            res.data.dataList[targetIndex].similarity = res.data.priority[targetIndex].similarity;
                            res.data.dataList[targetIndex].specialTitle = '产品';
                            res.data.dataList[targetIndex].customizedType = 4;
                            compareSimilarityList.push(res.data.dataList[targetIndex]);
                        }
                    }
                    // 获取符合要求的能力、场景、产品里匹配度最高的一项
                    if (compareSimilarityList.length !== 0) {

                        highestMatch = compareSimilarityList.reduce(
                            (max, item) => (item.similarity > max.similarity ? item : max),
                            compareSimilarityList[0]
                        )
                    } else {
                        highestMatch = {
                            name: item.key,
                        }
                    }
                    // 把匹配度最高的加入准备去定制的数组

                    // console.log('最终去定制的数组', data.goCustomizedList)
                })
            }

            await new Promise(resolve => setTimeout(() => {
                console.log('item.key', item.key)
                // 模拟循环调接口有没有拿到合适的能力，拿item.key和item.description
                // 搜没搜到能力
                if (compareSimilarityList.length !== 0) {
                    item.showTitle = '（' + highestMatch.name + '）'
                    item.title = highestMatch.name
                    item.specialTitle = highestMatch.specialTitle
                    if (highestMatch.specialTitle == '能力') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    } else if (highestMatch.specialTitle == '场景') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    } else if (highestMatch.specialTitle == '产品') {
                        item.description = highestMatch.descShow
                        item.id = highestMatch.id
                        item.slideUrls = highestMatch.slideUrls
                    }
                    item.searched = '已搜到'
                } else {
                    item.showTitle = '（小麟仍在努力学习，暂未能帮您定制成功）'
                    item.description = null
                    item.searched = '未搜到'
                    data.noSearchThinkAbilityList.push({
                        name: item.key
                    })
                }
                // console.log('没搜到的集合', data.noSearchThinkAbilityList)
                resolve();
            }, 500));
            if (data.continueProcess) {
                if (!data.abilityNamesList.includes(highestMatch.name)) {
                    console.log('不包含', data.abilityNamesList, highestMatch.name)
                    if (compareSimilarityList.length !== 0) {
                        data.goCustomizedList.push(highestMatch)
                    }
                    if (highestMatch.slideUrls && highestMatch.slideUrls.length !== 0) {
                        emit('contorlAddingPic', 'true')
                        // PPT的
                        emit('changeRightModuleType', 'PPT')
                        emit('openPPTPreview')
                        emit('addPPTSmallTitle', highestMatch.name)
                        if (highestMatch.slideUrls.length !== 0) {
                            data.goHistoryData.PPTList.push({
                                smallTitle: highestMatch.name
                            })
                        }
                        let arr = []
                        for (let i of highestMatch.slideUrls) {
                            arr.push({
                                img: i
                            })
                        }
                        emit('addPPT', arr)
                        for (let i of highestMatch.slideUrls) {
                            data.goHistoryData.PPTList.push({
                                img: i
                            })
                        }
                    }
                    data.abilityNamesList.push(highestMatch.name)
                    await typeWriterForItem(item, 'displayKey', item.key);
                    await typeWriterForItem(item, 'displayRealTitle', item.showTitle);
                    if (item.searched == '已搜到' && data.continueProcess) {
                        await typeWriterForItem(item, 'displayIntroduction', item.description);
                    }
                } else {
                    console.log('包含', data.abilityNamesList, highestMatch.name)
                    data.showNoSolutionThinkAbilityList.splice(data.showNoSolutionThinkAbilityList.length - 1, 1)
                }
            }


        }
        // 二、应用案例
        const noSolutionCustomized3 = () => {
            data.showBr7 = true
            return typeWriter('writingNoSolutionCustomized3', data.noSolutionCustomizedText3, 20).then(async () => {
                if (data.continueProcess) {
                    await writingNoSolutionPeopleCaseList()
                    await typeWriterSummarize()
                }
            });
        }
        // 依次掉接口查案例
        const writingNoSolutionPeopleCaseList = async () => {
            for (const ability of data.AIData.keyword.caseList) {
                const newItem = reactive({
                    ...ability,
                    displayText: '',
                    displayRealTitle: '',
                    displayIntroduction: '',
                    isNew: true,
                });
                if (data.continueProcess) {
                    data.showNoSolutionPeopleCasesList.push(newItem);
                    await processAddNoSolutionPeopleCase(newItem);
                }
            }
        }
        const processAddNoSolutionPeopleCase = async (item) => {
            let dataList = []
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 6,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    // solutionId: data.nowSolutionId,
                    aim: data.AIData.aim,
                }).then((res) => {
                    dataList = res.data.dataList
                    if (res.data.dataList.length !== 0) {
                        res.data.dataList[0].specialTitle = '案例'
                        res.data.dataList[0].customizedType = 6
                        data.goCustomizedList.push(res.data.dataList[0])
                        if (res.data.dataList[0].slideUrls && res.data.dataList[0].slideUrls.length !== 0) {
                            // PPT的
                            emit('addPPTSmallTitle', dataList[0].caseName)
                            if (res.data.dataList[0].slideUrls.length !== 0) {
                                data.goHistoryData.PPTList.push({
                                    smallTitle: dataList[0].caseName
                                })
                            }

                            let arr = []
                            for (let i of dataList[0].slideUrls) {
                                arr.push({
                                    img: i
                                })
                            }
                            emit('addPPT', arr)
                            for (let i of dataList[0].slideUrls) {
                                data.goHistoryData.PPTList.push({
                                    img: i
                                })
                            }
                        }
                    }
                    // console.log('最终定制', data.goCustomizedList)
                })
            }

            await new Promise(resolve => setTimeout(() => {
                // 搜没搜到案例
                if (dataList.length !== 0) {
                    item.searched = '已搜到'
                    item.name = '（' + dataList[0].caseName + '）'
                    item.id = dataList[0].id
                    item.slideUrls = dataList[0].slideUrls
                    item.title = dataList[0].caseName
                    item.specialTitle = '案例'
                } else {
                    item.searched = '未搜到'
                    item.unSearched = '很抱歉，平台素材有限，未能为您定制合适的' + item.name + '案例'
                    item.name = '（未能找到相关案例）'
                }
                resolve();
            }, 500));
            if (data.continueProcess) {
                await typeWriterForItem(item, 'displayText', item.key);
                await typeWriterForItem(item, 'displayRealTitle', item.name);
                if (dataList.length !== 0) {
                    await typeWriterForItem(item, 'displayIntroduction', dataList[0].descShow);
                }
            }
        }
        const baseURL = getBaseUrl();


        // AI3.0
        // ☆☆☆☆☆☆ 新的没有搜到方案的情况 ↓ ☆☆☆☆☆☆
        const newWritingNoSolution = () => {
            // data.isInternetOver = true
            data.showGuideWords = false
            data.goHistoryData.busDirect = '行业'
            data.goHistoryData.solutionName = data.AIData.keyword.solutionList[0].key
            if (data.continueProcess) {
                // let text = '为了更好的帮您定制方案，我正在为您的' + data.AIData.keyword.solutionList[0].key + '联网查询一些信息：'
                let text = '由于平台内没有合适的成品方案，我将为您主动生成方案PPT'
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.showPeopleWords1Index < text.length) {
                            data.showPeopleWords1 += text.charAt(data.showPeopleWords1Index);
                            data.showPeopleWords1Index++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            newWritingZhengceWords()
                        }
                    };
                    write();
                });
            }
        }
        // 生成日期+四位随机数的sessionId
        const generateNoSolutionSessionId = () => {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const randomNum = Math.floor(Math.random() * 9000 + 1000); // 1000-9999
            return `${year}${month}${day}${randomNum}`;
        };
        // 流式生成政策背景信息
        const newWritingZhengceWords = () => {
            data.streamChatSessionId = generateNoSolutionSessionId();
            data.goHistoryData.streamChatSessionId = data.streamChatSessionId
            console.log('data.streamChatSessionId', data.streamChatSessionId)
            let keyword = data.AIData.aim
            let token = localStorage.getItem('token') || sessionStorage.getItem('token') || ''
            let url = baseURL + 'view/streamChat/searchPolicy?keyword=' + keyword + '&sessionId=' + data.streamChatSessionId + '&solutionName=' + data.AIData.keyword.solutionList[0].key + '&token=' + token
            if (data.streamZhengceSource) {
                data.streamZhengceSource.close()
            }
            let text = ''
            const source = new EventSource(url);
            data.streamZhengceSource = source
            source.onmessage = function (event) {
                console.log('JSON.parse(event.data)', JSON.parse(event.data))
                let content = JSON.parse(event.data).content;
                text += content;
                data.zhengceText = marked(text)
            }
            source.onerror = function (err) {
                if (data.streamZhengceSource && data.streamZhengceSource.readyState === EventSource.CLOSED) {
                    console.log('连接已由用户关闭')
                    return
                }
                data.isInternetOver = true
                data.writingGetIntentionTextText = '数据已查询完毕，我正在为您生成' + data.AIData.keyword.solutionList[0].key + '方案的大纲:'
                source.close();
                data.streamZhengceSource = null
                newWritingNoSolutionWords()
            }
        }
        // 流式生成PPT大纲
        const newWritingNoSolutionWords = () => {
            data.writingOutlineTextLoading = true
            console.log('新的没有搜到方案的情况')
            // 生产是60003
            // 测试是60004
            console.log('data.AIData', data.AIData)
            let keyword = data.AIData.aim
            // 获取token，可以从localStorage、sessionStorage或者vuex store中获取
            let token = localStorage.getItem('token') || sessionStorage.getItem('token') || ''
            // 连lzf
            let url = baseURL + 'view/streamChat/pptMaker?keyword=' + keyword + '&sessionId=' + data.streamChatSessionId + '&solutionName=' + data.AIData.keyword.solutionList[0].key + '&token=' + token
            // 直接连荣洋
            // let url = 'http://223.109.220.185:60004/aiict_server/onlineChat?keyword=' + keyword + '+&sessionId=' + sessionId + '&solutionName=' + data.AIData.keyword.solutionList[0].key + '&enableSearch=true&token=' + token
            if (data.streamPPTSource) {
                data.streamPPTSource.close()
            }
            let text = ''
            const source = new EventSource(url);
            data.streamPPTSource = source
            source.onmessage = function (event) {
                console.log('JSON.parse(event.data)', JSON.parse(event.data))
                data.showOutlineText = true
                data.isAIWriting = false
                let content = JSON.parse(event.data).content;
                text += content;
                data.outlineText = marked(text)
            }
            source.onerror = function (err) {
                if (data.streamPPTSource && data.streamPPTSource.readyState === EventSource.CLOSED) {
                    console.log('连接已由用户关闭')
                    return
                }
                data.goHistoryData.outlineText = data.outlineText
                data.writingOutlineWords = 'PPT大纲生成已完成'
                console.log('连接已关闭,走接下来的路线')
                getOutlinejson()
                source.close()
                data.streamPPTSource = null
            }
        }
        // 流式生成要素思考信息
        const newWritingThinkingWords = () => {
            // 生产是60003
            // 测试是60004
            console.log('data.AIData', data.AIData)
            let keyword = data.AIData.aim
            // 获取token，可以从localStorage、sessionStorage或者vuex store中获取
            let token = localStorage.getItem('token') || sessionStorage.getItem('token') || ''
            // 连lzf
            let url = baseURL + 'view/streamChat/commonThinking?keyword=' + keyword + '&sessionId=' + data.streamChatSessionId + '&solutionName=' + data.AIData.keyword.solutionList[0].key + '&token=' + token
            // 直接连荣洋
            // let url = 'http://223.109.220.185:60004/aiict_server/onlineChat?keyword=' + keyword + '+&sessionId=' + sessionId + '&solutionName=' + data.AIData.keyword.solutionList[0].key + '&enableSearch=true&token=' + token
            if (data.streamThinkingource) {
                data.streamThinkingource.close()
            }
            let text = ''
            const source = new EventSource(url);
            data.streamThinkingource = source
            source.onmessage = function (event) {
                console.log('连接已建立')
                data.showThinkingArea = true
                // console.log('JSON.parse(event.data)', JSON.parse(event.data))
                let content = JSON.parse(event.data).content;
                text += content;
                data.thinkingText = marked(text)

                // 自动滚动到底部
                nextTick(() => {
                    const thinkingTextElements = document.querySelectorAll('.thinkingTextStyle');
                    thinkingTextElements.forEach(element => {
                        if (element) {
                            element.scrollTop = element.scrollHeight;
                        }
                    });
                })
            }
            source.onerror = function (err) {
                data.thinkingOver = true
                if (data.streamThinkingource && data.streamThinkingource.readyState === EventSource.CLOSED) {
                    console.log('连接已由用户关闭')
                    return
                }
                console.log('连接已关闭,走接下来的路线')
                source.close()

                // 连接结束时，确保滚动到底部
                nextTick(() => {
                    const thinkingTextElements = document.querySelectorAll('.thinkingTextStyle');
                    thinkingTextElements.forEach(element => {
                        if (element) {
                            element.scrollTop = element.scrollHeight;
                        }
                    });
                })
            }
        }
        const closeStreamThinkingource = () => {
            data.isThinkingOver = true
            if (data.streamThinkingource && typeof data.streamThinkingource.close === 'function') {
                data.streamThinkingource.close()
                data.streamThinkingource = null
            }
        }
      const writingThinkingWords = () => {
        if (data.continueProcess) {
          let text = '小麟正在为您的需求进行要素思考，请稍后...'
          return typeWriter('showPeopleThinkingWords', text, 20).then(() => {
            // 隐藏要素思考，则 data.thinkingOver = true
            newWritingThinkingWords()
            // if (data.isChangeAI2) {
            //     data.thinkingOver = true
            // } else {
            //     newWritingThinkingWords()
            // }
          });
        }
      }
        const getOutlinejson = () => {
            getChatJSON({
                // 拼0是新的，不拼是之前的
                sessionId: data.streamChatSessionId + '0'
            }).then((res) => {
                // 切换成表单
                // data.isOutlineTextOver = true
                console.log('res', res)
                data.pptJsonObj = res.data;
                // newNosolutionGetAIElementPoints()
                writingBigTitleSceneText()
            })
        }
        // 打印大标题：应用场景
        const writingBigTitleSceneText = () => {
            // 打开右侧PPT预览窗口
            // // 3.0暂时不放缩略图
            // emit('changeRightModuleType', 'PPT')
            // emit('clearPPTList')
            // emit('contorlAddingPic', 'true')
            let text = '四、应用场景'
            if (data.continueProcess) {
                return new Promise(async (resolve) => {
                    const write = async () => {
                        if (data.bigTitleSceneIndex < text.length) {
                            data.writingABigTitleScene += text.charAt(data.bigTitleSceneIndex);
                            data.bigTitleSceneIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            // 流式生成要素思考信息
                            writingThinkingWords()
                            let arr = []
                            for (let i of data.peopleSaidAbilityList) {
                                arr.push(i.key)
                            }
                            // AI要素思考接口给的能力
                            await getPPTElementExpand({
                                desc: data.outlineText,
                                elements: arr,
                            }).then((res) => {
                                // console.log('res', res)
                                if (res.data.elementList.length !== 0) {
                                    for (let i of res.data.elementList) {
                                        data.goSearchAbilityList.push(i)
                                    }
                                }
                            })

                            if (data.continueProcess) {
                                // 用要素思考去召回结果
                                await writingThinkAbilityList()
                            }
                            if (data.continueProcess) {
                                // 如果场景任何东西都没有
                                if (data.showThinkAbilityList.length == 0) {
                                    await writingNoThinkingAbilityText()
                                    await typeWritingNoSearchAbilityList()
                                }
                            }
                            if (data.continueProcess) {
                                await writingBigTitleCaseText()
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 场景没有搜到东西的时候打字
        const writingNoThinkingAbilityText = () => {
            closeStreamThinkingource()
            let text = '小麟希望为您的材料中编写以下方案场景，但小麟的知识库中似乎还没有用于编写这些方案场景的参考数据，得麻烦您自行补充了：'
            if (data.continueProcess) {
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.noThinkingAbilityTextIndex < text.length) {
                            data.noThinkingAbilityText += text.charAt(data.noThinkingAbilityTextIndex);
                            data.noThinkingAbilityTextIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                        }
                    };
                    write();
                });
            }
        }
        // 打印大标题：应用案例
        const writingBigTitleCaseText = () => {
            // 存场景列表和没搜到场景的原因
            data.goHistoryData.newNoSolutionShowThinkAbilityList = data.showThinkAbilityList
            data.goHistoryData.newNoSolutionShowNoSearchAbilityList = data.showNoSearchAbilityList
            let text = '五、应用案例'
            if (data.continueProcess) {
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.bigTitleCaseIndex < text.length) {
                            data.writingABigTitleCase += text.charAt(data.bigTitleCaseIndex);
                            data.bigTitleCaseIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                writingNewNoSolutionPeopleCasesList()
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 获取没有搜到方案的案例
        // 依次搜用户提到的案例
        const writingNewNoSolutionPeopleCasesList = async () => {
            let arr = []
            if (data.AIData.keyword.caseList && data.AIData.keyword.caseList.length !== 0) {
                for (let i of data.AIData.keyword.caseList) {
                    arr.push({
                        description: i.description,
                        key: i.key,
                        name: i.key,
                    })
                }
                for (const ability of arr) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                        displayRealTitle: '',
                        displayIntroduction: '',
                        displayNoSearchWords: '',
                        displayCaseList: [],
                        isNew: true,
                    });
                    if (data.continueProcess) {
                        data.showPeopleCasesList.push(newItem);
                        await getNewNoSolutionCase1(newItem);
                    }
                }
                await newWritingNoSolutionResult()
            } else if (!data.AIData.keyword.caseList) {
                console.log('没有搜到案例')
                arr = [{
                    description: data.AIData.keyword.solutionList[0].description,
                    key: data.AIData.keyword.solutionList[0].key,
                    name: data.AIData.keyword.solutionList[0].key,
                }]
                for (const ability of arr) {
                    const newItem = reactive({
                        ...ability,
                        displayText: '',
                        displayRealTitle: '',
                        displayIntroduction: '',
                        displayNoSearchWords: '',
                        displayCaseList: [],
                        isNew: true,
                    });
                    if (data.continueProcess) {
                        data.showPeopleCasesList.push(newItem);
                        await getNewNoSolutionCase2(newItem);
                    }
                }
                await newWritingNoSolutionResult()
            }
        }
        // 用户提到了案例
        const getNewNoSolutionCase1 = async (item) => {
            let dataList = []
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 6,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    // solutionId: data.nowSolutionId,
                    aim: data.AIData.aim
                }).then((res) => {
                    // console.log('查案例', res)
                    dataList = res.data.dataList
                    // if (res.data.dataList.length !== 0) {
                    //     res.data.dataList[0].specialTitle = '案例'
                    //     res.data.dataList[0].customizedType = 6
                    //     data.goCustomizedList.push(res.data.dataList[0])
                    // }
                })
            }
            if (data.continueProcess) {
                if (dataList.length !== 0) {
                    // 先打印item.name标题
                    console.log('案例标题：', item.name)

                    // 初始化displayCaseList数组
                    if (!item.displayCaseList) {
                        item.displayCaseList = []
                    }

                    // 遍历dataList，为每个案例创建显示对象
                    for (let i = 0; i < dataList.length; i++) {
                        const caseData = dataList[i]
                        const caseItem = {
                            displayCaseName: '',
                            displayCaseDescription: '',
                            id: caseData.id,
                        }
                        item.displayCaseList.push(caseItem)

                    }

                    // if (dataList[0].slideUrls && dataList[0].slideUrls.length !== 0) {
                    //     // PPT的
                    //     emit('addPPTSmallTitle', dataList[0].caseName)
                    //     if (dataList[0].slideUrls.length !== 0) {
                    //         data.goHistoryData.PPTList.push({
                    //             smallTitle: dataList[0].caseName
                    //         })
                    //     }

                    //     let arr = []
                    //     for (let i of dataList[0].slideUrls) {
                    //         arr.push({
                    //             img: i
                    //         })
                    //     }
                    //     emit('addPPT', arr)
                    //     for (let i of dataList[0].slideUrls) {
                    //         data.goHistoryData.PPTList.push({
                    //             img: i
                    //         })
                    //     }
                    // }
                    // item.id = dataList[0].id
                    // item.slideUrls = dataList[0].slideUrls
                    // item.title = dataList[0].caseName
                    await typeWrite(item, 'displayText', item.name);

                    // 为每个案例执行打字效果
                    for (let i = 0; i < item.displayCaseList.length; i++) {
                        let arr = []
                        const caseItem = item.displayCaseList[i]
                        const caseData = dataList[i]
                        caseItem.hasReasult = '搜到了'
                        // // 3.0暂时不放缩略图
                        // if (caseData.slideUrls.length !== 0) {
                        //     data.goHistoryData.PPTList.push({
                        //         smallTitle: caseData.caseName
                        //     })
                        //     emit('addPPTSmallTitle', caseData.caseName)
                        //     for (let i of caseData.slideUrls) {
                        //         arr.push({
                        //             img: i
                        //         })
                        //         data.goHistoryData.PPTList.push({
                        //             img: i
                        //         })
                        //     }
                        //     emit('addPPT', arr)
                        // }
                        await typeWrite(caseItem, 'displayCaseName', '《' + caseData.caseName + '》：');
                        await typeWrite(caseItem, 'displayCaseDescription', caseData.descShow);
                    }

                } else if (dataList.length == 0) {
                    await typeWrite(item, 'displayText', item.name);
                    await typeWrite(item, 'displayNoSearchWords', '很抱歉，平台素材有限，未能为您查询到合适的' + item.name + '案例');
                }
            }
        }
        // 用户没提到案例，拿方案的去召回
        const getNewNoSolutionCase2 = async (item) => {
            // newWritingNoSolutionResult()
            console.log('没有搜到案例，拿方案的去召回')
            let dataList = []
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 6,
                    description: data.AIData.keyword.solutionList[0].description,
                    topK: 10,
                    name: data.AIData.keyword.solutionList[0].key,
                    // solutionId: data.nowSolutionId,
                    aim: data.AIData.aim
                }).then((res) => {
                    console.log('查案例', res)
                    if (res.data.dataList.length !== 0) {
                        for (let i = 0; i < res.data.dataList.length; i++) {
                            if (res.data.dataList[i].id = res.data.priority[i].id) {
                                res.data.dataList[i].similarity = res.data.priority[i].similarity
                            }
                        }
                        for (let i of res.data.dataList) {
                            if (i.similarity >= 0.52) {
                                dataList.push(i)
                            }
                        }
                    }
                })
            }
            if (data.continueProcess) {
                console.log('dataList111', dataList)
                if (dataList.length !== 0) {
                    if (!item.displayCaseList) {
                        item.displayCaseList = []
                    }
                    // 遍历dataList，为每个案例创建显示对象
                    for (let i = 0; i < dataList.length; i++) {
                        const caseData = dataList[i]
                        const caseItem = {
                            displayCaseName: '',
                            displayCaseDescription: '',
                            id: caseData.id,
                        }
                        item.displayCaseList.push(caseItem)
                    }
                    // PPT的一些东西
                    //
                    //
                    //
                    await typeWrite(item, 'displayText', item.name);
                    // 为每个案例执行打字效果
                    for (let i = 0; i < item.displayCaseList.length; i++) {
                        let arr = []
                        const caseItem = item.displayCaseList[i]
                        const caseData = dataList[i]
                        caseItem.hasReasult = '搜到了'
                        // // 3.0暂时不放缩略图
                        // if (caseData.slideUrls.length !== 0) {
                        //     data.goHistoryData.PPTList.push({
                        //         smallTitle: caseData.caseName
                        //     })
                        //     emit('addPPTSmallTitle', caseData.caseName)
                        //     for (let i of caseData.slideUrls) {
                        //         arr.push({
                        //             img: i
                        //         })
                        //         data.goHistoryData.PPTList.push({
                        //             img: i
                        //         })
                        //     }
                        //     emit('addPPT', arr)
                        // }
                        await typeWrite(caseItem, 'displayCaseName', '《' + caseData.caseName + '》：');
                        await typeWrite(caseItem, 'displayCaseDescription', caseData.descShow);
                    }
                } else if (dataList.length == 0) {
                    await typeWrite(item, 'displayText', item.name);
                    await typeWrite(item, 'displayNoSearchWords', '很抱歉，平台素材有限，未能为您查询到合适的' + item.name + '案例');
                }
            }
        }
        // 结尾句
        const newWritingNoSolutionResult = () => {
            emit('contorlAddingPic', 'false')
            data.goHistoryData.pptJsonObj = data.pptJsonObj
            data.goHistoryData.newNoSolutionShowPeopleCasesList = data.showPeopleCasesList
            console.log('data.goHistoryData', data.goHistoryData)
            storeChatResult({
                sessionId: data.sessionId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
                data.chatId = res.data
                data.goHistoryData.chatId = res.data
            })
            if (data.continueProcess) {
                let text = '小麟已根据这份大纲，帮您自主生成了一份完整方案，点击下方按钮即可下载！'
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.showPeopleWords2Index < text.length) {
                            data.showPeopleWords2 += text.charAt(data.showPeopleWords2Index);
                            data.showPeopleWords2Index++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            writingOver()
                            data.writingOutlineTextLoading = false
                            data.showOutlineCustomizedBtn = true
                        }
                    };
                    write();
                });
            }
        }
        // AI3.0的确认按钮
        const confirmOutlineText = () => {
            let PPTData = {
                pptJsonObj: data.pptJsonObj,
                showThinkAbilityList: data.showThinkAbilityList,
                showPeopleCasesList: data.showPeopleCasesList,
                sessionId: data.streamChatSessionId,
            }
            handlePPTData(PPTData, data.AIData.keyword.solutionList[0].key)
            data.getPPTLoading = true
            getAIPPT(handlePPTData(PPTData, data.AIData.keyword.solutionList[0].key)).then((res) => {
                data.getPPTLoading = false
                console.log('res', res)
                const href = res.msg;
                let windowOrigin = window.location.origin;
                let token = localStorage.getItem("token");
                let newHref = href;
                if (href.includes(windowOrigin)) {
                    newHref = "/portal" + href.split(windowOrigin)[1]
                }
                window.open(windowOrigin + newHref + "?token=" + token);
            })
            // 以后支持修改的话要重新储存聊天记录
            // data.goHistoryData.pptJsonObj = data.pptJsonObj
            // storeChatResult({
            //     sessionId: data.sessionId,
            //     aimDescription: JSON.stringify(data.goHistoryData)
            // }).then((res) => {
            //     console.log('res', res)
            //     data.chatId = res.data
            //     data.goHistoryData.chatId = res.data
            // })
        }
        // AI3.0聊天记录里的确认按钮
        const historyStreamChatContinue = (historyData) => {
            console.log('historyData', historyData)
            let PPTData = {
                pptJsonObj: historyData.pptJsonObj,
                showThinkAbilityList: historyData.newNoSolutionShowThinkAbilityList,
                showPeopleCasesList: historyData.newNoSolutionShowPeopleCasesList,
                sessionId: historyData.streamChatSessionId,
            }
            handlePPTData(PPTData, historyData.solutionName)
            data.geHistoryPPTLoading = true
            getAIPPT(handlePPTData(PPTData, historyData.solutionName)).then((res) => {
                data.geHistoryPPTLoading = false
                console.log('res', res)
                const href = res.msg;
                let windowOrigin = window.location.origin;
                let token = localStorage.getItem("token");
                let newHref = href;
                if (href.includes(windowOrigin)) {
                    newHref = "/portal" + href.split(windowOrigin)[1]
                }
                window.open(windowOrigin + newHref + "?token=" + token);
            })
        }
        // 处理PPT数据
        const handlePPTData = (obj, solutionName) => {
            console.log('obj', obj)
            let goPPTData = {
                solutionName: solutionName,
                caseIds: [],
                sceneInfoIds: [],
                solutionOverview: obj.pptJsonObj.solutionOverview,
                demandAnalysis: obj.pptJsonObj.demandAnalysis,
                backgroundPolicy: obj.pptJsonObj.backgroundPolicy,
                sessionId: obj.sessionId,
            }
            if (obj.showThinkAbilityList && obj.showThinkAbilityList.length !== 0) {
                for (let i of obj.showThinkAbilityList) {
                    if (i.specialTitle == '产品') {
                        i.classify = 0
                    } else if (i.specialTitle == '能力') {
                        i.classify = 1
                    } else if (i.specialTitle == '场景') {
                        i.classify = 2
                    }
                    if (i.id) {
                        goPPTData.sceneInfoIds.push({
                            id: i.id,
                            classify: i.classify
                        })
                    }
                }
            }

            if (obj.showPeopleCasesList && obj.showPeopleCasesList.length !== 0) {
                for (let i of obj.showPeopleCasesList) {
                    if (i.displayCaseList && i.displayCaseList.length !== 0) {
                        for (let j of i.displayCaseList) {
                            goPPTData.caseIds.push(j.id)
                        }
                    }
                }
            }
            console.log('goPPTData1111111', goPPTData)
            return goPPTData
        }



        // ☆☆☆☆☆☆ 查商客的方案 ↓ ☆☆☆☆☆☆
        // 好的，我将为您直接定制一份xxxx场景包
        const typeWriterAcceptrecommendRecommend = () => {
            if (data.continueProcess) {
                let acceptRecommendText = '好的，我将为您直接定制一份' + data.AIData.keyword.productPackageList[0].key + '场景包'
                data.goHistoryData.fixedWords1 = acceptRecommendText
                return typeWriter('writingAcceptRecommendText', acceptRecommendText, 20).then(() => {
                    if (data.continueProcess) {
                        setTimeout(() => {
                            typeWriterAcceptrecommendRecommend2()
                        }, 1000);
                    }
                });
            }
        };
        // 您的xxxx场景包中已包含如下产品：
        const typeWriterAcceptrecommendRecommend2 = () => {
            if (data.continueProcess) {
                let acceptRecommendText = '您的' + data.AIData.keyword.productPackageList[0].key + '场景包中已包含如下产品：'
                data.goHistoryData.fixedWords2 = acceptRecommendText
                return typeWriter('writingAcceptRecommendText2', acceptRecommendText, 20).then(async () => {
                    if (data.continueProcess) {
                        if (data.continueProcess) {
                            // 打印产品包自带的需求方案和产品
                            await typeWriterProductBagList()
                        }
                        if (data.continueProcess && data.AIData.keyword.productList) {
                            // 如果用户提到了产品，则去单召回每一条产品
                            await typeWriterAddProduct()//只是打印 XX、补充产品 这几个字
                            await typeWriterPeopleSayProduct()
                        }
                        if (data.continueProcess) {
                            await typeWriterProductBagSummarize()
                        }
                        if (data.continueProcess) {
                            writingOver()
                        }
                    }
                });
            }
        };
        // 依次打印推荐的产品包下的需求方案和带的产品
        const typeWriterProductBagList = async () => {
            // console.log('data.AIRecommendSceneList2223', data.AIRecommendSceneList)
            let arr = []
            if (data.AIRecommendSceneList.length !== 0) {
                for (let i of data.AIRecommendSceneList) {
                    if (i.demandSchemeList.length !== 0) {
                        for (let j of i.demandSchemeList) {
                            arr.push(j)
                            // data.goHistoryData.choosedProductPackageList.push(j)
                        }
                    }
                    data.goCustomizedProductPackageList.push({
                        productId: i.id,
                        type: 1
                    })
                }
            }
            // 根据id去重，输出去重后的数组uniqueArr
            const uniqueArr = arr.filter((item, index, self) => self.findIndex(v => v.id === item.id) === index)
            data.goHistoryData.choosedProductPackageList = uniqueArr
            console.log('data.goHistoryData.choosedProductPackageList☆☆☆', arr)
            console.log('去重后的数组uniqueArr', uniqueArr)
            for (const ability of uniqueArr) {
                const newItem = reactive({
                    ...ability,
                    displayText: '',
                    demandProductList: ability.demandProductList.map(product => reactive({
                        ...product,
                        displayProductName: '',
                        // displayProductDescription:'',
                    }))
                });
                if (data.continueProcess) {
                    data.showProductPackageOwnList.push(newItem);
                    await processProductPackageOwnList(newItem);
                    // 在每个方案项目之间添加0.5秒的延迟
                    if (uniqueArr.indexOf(ability) < uniqueArr.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
            }
        }

        const processProductPackageOwnList = async (item) => {
            // 先显示标题
            await typeWrite(item, 'displayText', item.name + '产品');
            // 然后依次显示每个产品
            for (const product of item.demandProductList) {
                product.canPrinting = '可以'
                await typeWrite(product, 'displayProductName', product.name);
                // await typeWrite(product, 'displayProductDescription', product.description);
            }
        }
        const typeWriterAddProduct = async () => {
            if (data.continueProcess) {
                let addProductText = numberToChinese(data.showProductPackageOwnList.length + 1) + '、' + '补充产品'
                return typeWriter('writingAddProductText', addProductText, 20).then(() => {

                });
            }
        }
        const typeWriterPeopleSayProduct = async () => {
            // 如果用户提到了产品，则去单召回每一条产品
            for (const ability of data.AIData.keyword.productList) {
                const newItem = reactive({
                    ...ability,
                    displayText: '',
                });
                data.showPeopleSayProductList.push(newItem);
                if (data.continueProcess) {
                    await processAddPeopleSayProduct(newItem);
                }
            }
        }
        const processAddPeopleSayProduct = async (item) => {
            let dataList = []
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 5,
                    description: item.description,
                    topK: 10,
                    name: item.key,
                    aim: data.AIData.aim
                }).then((res) => {
                    if (res.data.dataList.length !== 0) {
                        dataList.push(res.data.dataList[0])
                    }
                })
            }
            if (data.continueProcess) {
                if (dataList.length !== 0) {
                    //找到了产品
                    await typeWriterForItem(item, 'displayText', dataList[0].name);
                    data.goCustomizedProductPackageList.push({
                        productId: dataList[0].id,
                        type: 2,
                        specialType: '补充'
                    })
                    item.id = dataList[0].id
                    item.specialTitle = '商客产品'
                    item.specialType = '补充'
                    data.goHistoryData.SupplementaryProductList.push({
                        name: dataList[0].name,
                        id: dataList[0].id,
                        specialType: '补充'
                    })
                    item.searched = '已搜到'
                } else {
                    item.searched = '未搜到'
                }
            }
        }
        // 用户自由定制的开始（正在分析需求并规划场景包的产品组合......）
        const typeWriterFreeCustomized = async () => {
            if (data.continueProcess) {
                let freeCustomizedText = '正在分析需求......'
                data.goHistoryData.fixedWords1 = freeCustomizedText
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.freeCustomizedIndex < freeCustomizedText.length) {
                            data.writingFreeCustomizedText += freeCustomizedText.charAt(data.freeCustomizedIndex);
                            data.freeCustomizedIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                setTimeout(() => {
                                    typeWriterFreeCustomized2()
                                }, 1000);
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 已分析需求，即将开始xxx场景包的生成：
        const typeWriterFreeCustomized2 = async () => {
            if (data.continueProcess) {
                let freeCustomizedText2 = '已分析需求，即将开始' + data.AIData.keyword.productPackageList[0].key + '场景包的生成：'
                data.goHistoryData.fixedWords2 = freeCustomizedText2
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.freeCustomizedIndex2 < freeCustomizedText2.length) {
                            data.writingFreeCustomizedText2 += freeCustomizedText2.charAt(data.freeCustomizedIndex2);
                            data.freeCustomizedIndex2++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                typeWriterFreeCustomized3()
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 场景包名称：
        const typeWriterFreeCustomized3 = async () => {
            if (data.continueProcess) {
                let text = '场景包名称：'
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.freeCustomizedIndex3 < text.length) {
                            data.writingFreeCustomizedText3 += text.charAt(data.freeCustomizedIndex3);
                            data.freeCustomizedIndex3++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                typeWriterProductPackageName()
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 具体场景包的名称
        const typeWriterProductPackageName = async () => {
            if (data.continueProcess) {
                let productPackageName = '《' + data.AIData.keyword.productPackageList[0].key + '场景包》'
                data.goHistoryData.fixedWords3 = data.AIData.keyword.productPackageList[0].key
                return new Promise((resolve) => {
                    const write = async () => {
                        if (data.productPackageNameIndex < productPackageName.length) {
                            data.writingProductPackageName += productPackageName.charAt(data.productPackageNameIndex);
                            data.productPackageNameIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            if (data.continueProcess) {
                                await typeWriterElementThinking()
                            }
                            if (data.continueProcess) {
                                await typeWriterProductBagSummarize()
                            }
                        }
                    };
                    write();
                });
            }
        }
        // 拿要素思考的要素数组来挨个掉接口拿产品
        const typeWriterElementThinking = async () => {
            let arr = [
                {
                    name: '集团宿舍经营管理',
                    description: '融入预订、分配、服务及数据分析等，形成集团宿舍管理的运营管理，实现宿舍经营管理的可持续发展'
                },
                {
                    name: '安消管理（企业宿舍）',
                    description: '宿舍安全消防管理是安全生活的重要组成部分，直接关系到生命财产安全。'
                },
            ]
            for (let i of arr) {
                i.productList = []
            }
            let index = 0;
            for (const item of arr) {
                const newItem = reactive({
                    ...item,
                    displayFixedText: '',
                    displayText: '',
                    displayDescription: '',
                    productList: [],
                    loadingWords: '',
                });
                if (data.continueProcess) {
                    data.showThinkProductList.push(newItem)
                    await processThinkProductList(newItem, index);
                }
                index++;
            }
        }
        const processThinkProductList = async (item, index) => {
            let dataList = []
            item.showLoadingWords = true
            if (data.continueProcess) {
                await typeWrite(item, 'displayFixedText', '● 场景需求' + numberToChinese(index + 1) + '：');
            }
            if (data.continueProcess) {
                await typeWrite(item, 'displayText', '<' + item.name + '>');
            }
            if (data.continueProcess) {
                await typeWrite(item, 'displayDescription', item.description);
            }
            if (data.continueProcess) {
                await typeWrite(item, 'loadingWords', '正在为您挑选满足该需求的产品...');
            }
            if (data.continueProcess) {
                await getRetrieveList({
                    knowledgeType: 5,
                    description: item.description,
                    topK: 10,
                    name: item.name,
                    aim: data.AIData.aim
                }).then(async (res) => {
                    item.showLoadingWords = false
                    if (res.data.dataList.length !== 0) {
                        dataList = res.data.dataList;
                        console.log('dataList☆☆☆☆', dataList);
                        for (let i of dataList) {
                            data.goCustomizedProductPackageList.push({
                                productId: i.id,
                                type: 2
                            })
                        }
                        // 为每个产品创建响应式对象
                        item.productList = dataList.map(product => reactive({
                            ...product,
                            displayProductName: '',
                            displayProductDescription: ''
                        }));

                        // 依次打印每个产品的name和descShow
                        for (const product of item.productList) {
                            if (dataList.length !== 0) {
                                product.searched = '已搜到'
                            }
                            if (data.continueProcess) {
                                await typeWrite(product, 'displayProductName', product.name);
                                await typeWrite(product, 'displayProductDescription', product.descShow);
                            }
                        }
                    }
                })
            }
        }
        // 商客总结语打字机
        const typeWriterProductBagSummarize = () => {
            data.goHistoryData.freeCustomizedProductList = data.showThinkProductList
            return new Promise(async (resolve) => {
                data.goHistoryData.goCustomizedProductBagList = data.goCustomizedProductPackageList
                data.goHistoryData.commenTile = data.commenTile
                storeChatResult({
                    sessionId: data.sessionId,
                    aimDescription: JSON.stringify(data.goHistoryData)
                }).then((res) => {
                    console.log('res', res)
                    data.chatId = res.data
                    data.goHistoryData.chatId = res.data
                })
                const write = async () => {
                    if (data.productBagsummarizeIndex < data.productBagsummarize.length) {
                        data.writingproductBagSummarizeText += data.productBagsummarize.charAt(data.productBagsummarizeIndex);
                        data.productBagsummarizeIndex++;
                        setTimeout(write, 40);
                    } else {
                        data.showJumpProductBagCustomizedBtn = true
                        await writingOver();
                        resolve();
                    }
                };
                write();
            });
        };
        // 商客去定制的确定按钮
        const isGoProductPackage = (type) => {
            if (type == '1') {
                // 跳转到商客定制页面
                data.showGoProductLoading = true
                console.log('ssssssssssssss', data.goCustomizedProductPackageList)
                toShopList({
                    productShoppingCarts: data.goCustomizedProductPackageList,
                    source: "2",
                    title: data.commenTile,
                }).then((res) => {
                    emit('changeResultType', '7')
                    emit('closeSpeakBox')
                    localStorage.setItem('goCustomizedSessionId', data.sessionId)
                    // Router.push({
                    //     path: "/newProject/newProject",
                    //     query: {
                    //         type: 10
                    //     }
                    // });
                    let href = window.location.origin + '/#/newProject/newProject?type=10'
                    console.log('href', href)
                    window.open(href, '_blank')
                    data.showGoProductLoading = false
                })

            } else {
                // 用户点了否,本次对话结束
                data.showJumpProductBagCustomizedBtn = false
                writingOver()
                emit('noGood')
                // clearSession().then((res) => {
                //     console.log('清除记忆', res)
                // })
            }
            // data.productPackageBtnDisabled = true
        }
        // ☆☆☆☆☆☆ AI识别成检索 ↓ ☆☆☆☆☆☆
        // AI 识别为检索后数据查询完毕的打字机
        const typeWriterAISearchOver = () => {
            storeChatResult({
                sessionId: data.sessionId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
                data.chatId = res.data
                data.goHistoryData.chatId = res.data
            })
            let text = ''
            let allLength = 0
            for (let i of data.goSearchPageList.solutionList) {
                allLength += i.solution.length
            }
            for (let i of data.goSearchPageList.abilityList) {
                allLength += i.ability.length
            }
            for (let i of data.goSearchPageList.sceneList) {
                allLength += i.scene.length
            }
            for (let i of data.goSearchPageList.productList) {
                allLength += i.product.length
            }
            for (let i of data.goSearchPageList.productPackageList) {
                allLength += i.productBag.length
            }
            console.log('allLength', allLength)
            console.log('data.goSearchPageList', data.goSearchPageList)
            if (allLength == 0) {
                text = '很抱歉，小麟没能找到您想要的内容，小麟将继续努力学习，争取早日帮到您！'
            } else {
                text = '检索已完成，正在打开结果页面'
            }
            return new Promise((resolve) => {
                const write = () => {
                    if (data.AIAnswerSearchOverIndex < text.length) {
                        data.writingAIAnswerSearchOverText += text.charAt(data.AIAnswerSearchOverIndex);
                        data.AIAnswerSearchOverIndex++;
                        setTimeout(write, 40);
                    } else {
                        resolve();
                        if (allLength == 0) {

                        } else {
                            data.jumpingSearch = true
                            // console.log('跳转至检索页')
                            localStorage.setItem('goCustomizedSessionId', data.sessionId)
                            // Router.push({
                            //     path: "/newProject/newProject",
                            //     query: {
                            //         type: 6
                            //     }
                            // });
                            let href = window.location.origin + '/#/newProject/newProject?type=6'
                            console.log('href', href)
                            window.open(href, '_blank')
                        }
                        writingOver()
                    }
                };
                write();
            });
        };


        // 总结语打字机
        const typeWriterSummarize = () => {
            emit('contorlAddingPic', 'false')
            data.solutionStatus = '定制完成'
            console.log('zdl', data.goCustomizedList)
            // console.log('data.goHistoryData.PPTList111111', data.goHistoryData.PPTList)
            data.goHistoryData.goCustomizedList = data.goCustomizedList
            data.showBr4 = true
            // 来判断有没有东西去定制
            let arr = []
            for (let i of data.goCustomizedList) {
                if (i.specialTitle == '方案') {
                    arr.push({
                        schemeId: i.id,
                        type: 1
                    })
                }
                if (i.specialTitle == '能力') {
                    arr.push({
                        schemeId: i.id,
                        type: 2
                    })
                }
                if (i.specialTitle == '场景') {
                    arr.push({
                        schemeId: i.id,
                        type: 3
                    })
                }
                if (i.specialTitle == '产品') {
                    arr.push({
                        schemeId: i.id,
                        type: 4
                    })
                }
                if (i.specialTitle == '案例') {
                    arr.push({
                        schemeId: i.id,
                        type: 5
                    })
                }
            }
            // 把直接传过去调接口的数组arr进行去重
            const seen = new Set();
            const uniqueArr = arr.filter(item => {
                const identifier = `${item.schemeId}|${item.type}`;
                return seen.has(identifier) ? false : seen.add(identifier);
            });
            console.log('uniqueArr☆☆☆☆', uniqueArr)
            data.goHistoryData.goCustomizedUniqueList = uniqueArr
            if (data.nowSolutionId) {
                // 搜到了方案
                data.summarize = '小麟已为您定制好了方案，您可点击"'
            } else {
                // 没搜到方案
                if (uniqueArr.length !== 0) {
                    data.summarize = '小麟已为您组合好了方案，您可点击"'
                } else {
                    // 啥玩意都没有去定制的
                    data.summarize = '抱歉，这个方案的内容小麟还没学过，未能为您定制成功，小麟将继续努力学习，争取早日帮到您！'
                    data.goHistoryData.PPTList = []
                }

                for (let i of data.showNoSolutionThinkAbilityList) {
                    data.goHistoryData.abilityList.push({
                        id: i.id,
                        name: i.displayKey,
                        realName: i.displayRealTitle,
                        description: i.displayIntroduction,
                        searched: i.searched,
                        specialTitle: i.specialTitle,
                        // isSolutionOwn: false,
                        title: i.title,
                        slideUrls: i.slideUrls,
                        isNew: true,
                    })
                }
                for (let i of data.showNoSolutionPeopleCasesList) {
                    data.goHistoryData.peopleCaseList.push({
                        id: i.id,
                        name: i.displayText,
                        realName: i.displayRealTitle,
                        description: i.displayIntroduction,
                        searched: i.searched,
                        specialTitle: i.specialTitle,
                        // isSolutionOwn: false,
                        title: i.title,
                        slideUrls: i.slideUrls,
                        isNew: true,
                    })
                }
            }
            return typeWriter('writingSummarizeText', data.summarize, 20).then(async () => {
                console.log('goHistoryData☆☆☆☆', data.goHistoryData)
                if (data.nowSolutionId) {
                    if (data.continueProcess) {
                        typeWriterSummarize2()
                    }
                } else {
                    if (uniqueArr.length !== 0) {
                        if (data.continueProcess) {
                            typeWriterSummarize2()
                        }
                    } else {
                        storeChatResult({
                            sessionId: data.sessionId,
                            aimDescription: JSON.stringify(data.goHistoryData)
                        }).then((res) => {
                            console.log('res', res)
                            data.chatId = res.data
                            data.goHistoryData.chatId = res.data
                        })
                        // console.log('data.goCustomizedList☆☆☆☆', data.goCustomizedList)
                        if (uniqueArr.length !== 0) {
                            data.showJumpCustomizedBtn = true
                        } else {
                            data.showJumpCustomizedBtn = false
                        }
                        await writingOver();
                    }
                }
            });
        };
        // 为了打出不同颜色的"确认"和"删除"做的一些操作
        const typeWriterSummarize2 = () => {
            let writerSummarize2 = '确定'
            if (data.continueProcess) {
                return typeWriter('writingSummarize2Text', writerSummarize2, 20).then(() => {
                    typeWriterSummarize3()
                });
            }
        }
        const typeWriterSummarize3 = () => {
            let writerSummarize3 = ''
            if (data.nowSolutionId) {
                writerSummarize3 = '"按钮去看看生成结果哦！'
            } else {
                writerSummarize3 = '"按钮去看看生成结果哦！小麟也将继续努力学习，争取早日能为您定制完整方案！'
            }

            if (data.continueProcess) {
                return typeWriter('writingSummarize3Text', writerSummarize3, 20).then(() => {
                    typeWriterSummarize4()
                });
            }
        }
        const typeWriterSummarize4 = () => {
            let writerSummarize4 = '如有您不满意的地方，可动动鼠标去'
            if (data.continueProcess) {
                return typeWriter('writingSummarize4Text', writerSummarize4, 20).then(() => {
                    typeWriterSummarize5()
                });
            }
        }
        const typeWriterSummarize5 = () => {
            let writerSummarize5 = '删除'
            if (data.continueProcess) {
                return typeWriter('writingSummarize5Text', writerSummarize5, 20).then(() => {
                    typeWriterSummarize6()
                });
            }
        }
        const typeWriterSummarize6 = () => {
            let writerSummarize6 = '哦！'
            if (data.continueProcess) {
                return typeWriter('writingSummarize6Text', writerSummarize6, 20).then(() => {
                    writingOver()
                    storeChatResult({
                        sessionId: data.sessionId,
                        aimDescription: JSON.stringify(data.goHistoryData)
                    }).then((res) => {
                        console.log('res', res)
                        data.chatId = res.data
                        data.goHistoryData.chatId = res.data
                    })
                    // console.log('data.goCustomizedList☆☆☆☆', data.goCustomizedList)
                    if (data.goHistoryData.goCustomizedUniqueList.length !== 0) {
                        data.showJumpCustomizedBtn = true
                    } else {
                        data.showJumpCustomizedBtn = false
                    }
                });
            }
        }
        // 当意图为定制方案的时候，处理数据
        // AI3.0    (切换的时候同步切换要素流式)
        // const getAICustomizedData = async () => {
        //     return new Promise((resolve, reject) => {
        //         // console.log('')
        //         // 把keyword里面可能存在的sceneList、abilityList、productList进行遍历，把里面每一项的description、key掏出来组成一个新的数组，用于去重
        //         const descriptionAndkeyArr = [
        //             ...(data.AIData.keyword.sceneList || []).map(({ description, key }) => ({ description, key })),
        //             ...(data.AIData.keyword.abilityList || []).map(({ description, key }) => ({ description, key })),
        //             ...(data.AIData.keyword.productList || []).map(({ description, key }) => ({ description, key }))
        //         ];
        //         // console.log('去重前的数组', descriptionAndkeyArr)
        //         // 把descriptionAndkeyArr数组进行去重
        //         const seen = new Set();
        //         const uniqueDescriptionAndkeyArr = descriptionAndkeyArr.filter(item => {
        //             const identifier = `${item.description}|${item.key}`;
        //             return seen.has(identifier) ? false : seen.add(identifier);
        //         });
        //         data.peopleSaidAbilityList = uniqueDescriptionAndkeyArr
        //         console.log('人说的去重后的数组', uniqueDescriptionAndkeyArr)
        //         // 单召回接口看有没有方案
        //         getRetrieveList({
        //             knowledgeType: 1,
        //             description: data.AIData.keyword.solutionList[0].key,
        //             topK: 10,
        //             name: data.AIData.keyword.solutionList[0].key,
        //             type: 1,
        //         }).then((res) => {
        //             data.solutionStatus = '选方案中'
        //             if (res.data.dataList.length !== 0) {
        //                 let hasItemWithIsSensitiveZero = false;
        //                 // let firstIsSensitiveZeroIndex = -1; // 第一个不是敏感方案的方案索引，默认索引为-1，表示未找到
        //                 // 找到第一个非敏感方案才可以去定制
        //                 if (res.data.dataList && Array.isArray(res.data.dataList)) {
        //                     hasItemWithIsSensitiveZero = res.data.dataList.some(item => item && item.isSensitive === 0);
        //                     // 寻找第一个不是敏感方案的方案索引
        //                     // if (hasItemWithIsSensitiveZero) {
        //                     //     firstIsSensitiveZeroIndex = res.data.dataList.findIndex(item => item && item.isSensitive === 0);
        //                     // }
        //                 }
        //                 // // 你可以在这里使用 firstIsSensitiveZeroIndex，例如：
        //                 // console.log('第一个isSensitive为0的对象的索引是:', firstIsSensitiveZeroIndex);

        //                 // 有非敏感方案
        //                 if (hasItemWithIsSensitiveZero) {

        //                     // 剔除isSensitive不为0的项组成新数组
        //                     const filteredDataList = res.data.dataList.filter(item => item && item.isSensitive === 0);
        //                     console.log('所有不是敏感方案的方案', filteredDataList);
        //                     data.showSolutionList = filteredDataList
        //                     typeWriterPromptChooseSolution()

        //                 } else {
        //                     // 没有非敏感方案,即没搜到方案
        //                     emit('clearPPTList')
        //                     data.nowSolutionId = null
        //                     data.goHistoryData.hasSolution = false
        //                     data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
        //                     // 老的没有搜到方案的情况
        //                     data.showAnalysisWords = false
        //                     writingNoSolutionWords()
        //                     // newWritingNoSolution()
        //                 }
        //             } else {
        //                 // 没搜到方案
        //                 emit('clearPPTList')
        //                 data.nowSolutionId = null
        //                 data.goHistoryData.hasSolution = false
        //                 data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
        //                 // 老的没有搜到方案的情况
        //                 data.showAnalysisWords = false
        //                 writingNoSolutionWords()
        //                 // newWritingNoSolution()
        //             }
        //         })
        //     })
        // }
        // AI2.0
        const getAICustomizedData = async () => {
            if (data.isChangeAI2) {
                return new Promise((resolve, reject) => {
                    // console.log('')
                    // 把keyword里面可能存在的sceneList、abilityList、productList进行遍历，把里面每一项的description、key掏出来组成一个新的数组，用于去重
                    const descriptionAndkeyArr = [
                        ...(data.AIData.keyword.sceneList || []).map(({ description, key }) => ({ description, key })),
                        ...(data.AIData.keyword.abilityList || []).map(({ description, key }) => ({ description, key })),
                        ...(data.AIData.keyword.productList || []).map(({ description, key }) => ({ description, key }))
                    ];
                    // console.log('去重前的数组', descriptionAndkeyArr)
                    // 把descriptionAndkeyArr数组进行去重
                    const seen = new Set();
                    const uniqueDescriptionAndkeyArr = descriptionAndkeyArr.filter(item => {
                        const identifier = `${item.description}|${item.key}`;
                        return seen.has(identifier) ? false : seen.add(identifier);
                    });
                    data.peopleSaidAbilityList = uniqueDescriptionAndkeyArr
                    console.log('人说的去重后的数组', uniqueDescriptionAndkeyArr)
                    // 单召回接口看有没有方案
                    getRetrieveList({
                        knowledgeType: 1,
                        description: data.AIData.keyword.solutionList[0].key,
                        topK: 10,
                        name: data.AIData.keyword.solutionList[0].key,
                        type: 1,
                    }).then((res) => {
                        console.log('ssssssssssssss', res)
                        if (res.data.dataList.length !== 0) {
                            data.showAnalysisWords = false
                            let hasItemWithIsSensitiveZero = false;
                            let firstIsSensitiveZeroIndex = -1; // 默认索引为-1，表示未找到
                            // 找到第一个非敏感方案才可以去定制
                            if (res.data.dataList && Array.isArray(res.data.dataList)) {
                                hasItemWithIsSensitiveZero = res.data.dataList.some(item => item && item.isSensitive === 0);
                                if (hasItemWithIsSensitiveZero) {
                                    firstIsSensitiveZeroIndex = res.data.dataList.findIndex(item => item && item.isSensitive === 0);
                                }
                            }
                            // console.log('hasItemWithIsSensitiveZero☆☆☆☆', hasItemWithIsSensitiveZero);
                            // // 你可以在这里使用 firstIsSensitiveZeroIndex，例如：
                            // console.log('第一个isSensitive为0的对象的索引是:', firstIsSensitiveZeroIndex);

                            if (hasItemWithIsSensitiveZero) {
                                // PPT的
                                emit('changeRightModuleType', 'PPT')
                                emit('clearPPTList')
                                emit('contorlAddingPic', 'true')
                                // PPT的
                                emit('openPPTPreview')
                                data.hasSolution = true
                                data.nowSolutionDetail = res.data.dataList[firstIsSensitiveZeroIndex]//存当前方案的详情
                                data.nowSolutionId = res.data.dataList[firstIsSensitiveZeroIndex].id
                                data.nowSolutionName = res.data.dataList[firstIsSensitiveZeroIndex].name
                                data.nowSolutionDescription = res.data.dataList[firstIsSensitiveZeroIndex].description
                                data.solutionOverviewText5 = res.data.dataList[firstIsSensitiveZeroIndex].description
                                // 搜到了方案
                                res.data.dataList[firstIsSensitiveZeroIndex].specialTitle = '方案'
                                res.data.dataList[firstIsSensitiveZeroIndex].customizedType = 1
                                data.goCustomizedList.push(res.data.dataList[firstIsSensitiveZeroIndex])
                                //2025.6.19把方案里的场景和案例都拆开放进去
                                // 暂时隐藏
                                if (res.data.dataList[firstIsSensitiveZeroIndex].sceneInfoList.length !== 0) {
                                    for (let i of res.data.dataList[firstIsSensitiveZeroIndex].sceneInfoList) {
                                        i.specialTitle = '场景'
                                        data.goCustomizedList.push(i)
                                    }
                                }
                                // if (res.data.dataList[firstIsSensitiveZeroIndex].caseInfoList.length !== 0) {
                                //     for (let i of res.data.dataList[firstIsSensitiveZeroIndex].caseInfoList) {
                                //         i.specialTitle = '案例'
                                //         data.goCustomizedList.push(i)
                                //     }
                                // }

                                data.firstTitleText = '已分析用户需求，即将开始' + data.AIData.keyword.solutionList[0].key + '方案的生成：'
                                data.solutionOverviewText2 = '小麟正在为' + data.AIData.keyword.solutionList[0].key + '方案生成方案概述...'
                                // 存数据用于回显
                                data.goHistoryData.hasSolution = true
                                data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
                                data.goHistoryData.solutionName = res.data.dataList[firstIsSensitiveZeroIndex].name
                                data.goHistoryData.solutionOverview = res.data.dataList[firstIsSensitiveZeroIndex].description
                                if (res.data.dataList[firstIsSensitiveZeroIndex].slideUrls.length !== 0) {
                                    // PPT的
                                    emit('addPPTBigTitle', '方案概述')
                                    data.goHistoryData.PPTList.push({
                                        bigTitle: '方案概述'
                                    })
                                    let arr = []
                                    for (let i = 1; i < res.data.dataList[firstIsSensitiveZeroIndex].slideUrls.length; i++) {
                                        arr.push({
                                            img: res.data.dataList[firstIsSensitiveZeroIndex].slideUrls[i]
                                        })
                                        data.goHistoryData.PPTList.push({
                                            img: res.data.dataList[firstIsSensitiveZeroIndex].slideUrls[i]
                                        })
                                    }
                                    emit('addPPT', arr)

                                    // for (let i of res.data.dataList[firstIsSensitiveZeroIndex].slideUrls) {
                                    //     data.goHistoryData.PPTList.push({
                                    //         img: i
                                    //     })
                                    // }
                                }
                                typeWriterFirstTitleText()
                                if (res.data.dataList[firstIsSensitiveZeroIndex].sceneInfoList.length !== 0) {
                                    for (let i of res.data.dataList[firstIsSensitiveZeroIndex].sceneInfoList) {
                                        if (i.name) {
                                            data.solutionSceneList.push({
                                                name: i.name,
                                                description: i.descShow,
                                                slideUrls: i.slideUrls,
                                                id: i.id,
                                                specialTitle: '场景'
                                            })
                                        }
                                        i.specialTitle = "场景"
                                    }
                                }
                                if (res.data.dataList[firstIsSensitiveZeroIndex].caseInfoList.length !== 0) {
                                    for (let i of res.data.dataList[firstIsSensitiveZeroIndex].caseInfoList) {
                                        if (i.caseName) {
                                            data.solutionCaseList.push({
                                                name: i.caseName,
                                                description: i.descShow,
                                                slideUrls: i.slideUrls,
                                                id: i.id,
                                                specialTitle: '案例'
                                            })
                                        }
                                        i.specialTitle = "案例"
                                    }
                                }
                                // console.log('data.solutionCaseList', data.solutionCaseList)
                                // if (res.data.dataList[0].moduleBody.length !== 0) {
                                //     for (let i of res.data.dataList[0].moduleBody) {
                                //         if (i.type == 1) {
                                //             data.solutionOverviewText5 = i.moduleList[0].summary
                                //         }
                                //         // 把方案自带的应用场景加进去
                                //         if (i.type == 5) {
                                //             if (i.moduleList.length !== 0) {
                                //                 for (let j of i.moduleList) {
                                //                     if (j.name) {
                                //                         data.solutionSceneList.push({
                                //                             name: j.name,
                                //                             description: j.descShow
                                //                         })
                                //                     }
                                //                     j.specialTitle = "场景"
                                //                 }
                                //             }
                                //         }
                                //         if (i.type == 8) {
                                //             if (i.moduleList.length !== 0) {
                                //                 for (let j of i.moduleList) {
                                //                     if (j.projectName) {
                                //                         data.solutionCaseList.push({
                                //                             name: j.projectName,
                                //                             description: j.projectIntroduction,
                                //                         })
                                //                     }
                                //                     j.specialTitle = "案例"
                                //                 }
                                //             }
                                //         }
                                //     }
                                // }
                            } else {
                                // 没搜到方案
                                emit('clearPPTList')
                                data.nowSolutionId = null
                                data.goHistoryData.hasSolution = false
                                data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
                                data.showAnalysisWords = false
                                writingNoSolutionWords()
                            }
                        } else {
                            // 没搜到方案
                            emit('clearPPTList')
                            data.nowSolutionId = null
                            data.goHistoryData.hasSolution = false
                            data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
                            data.showAnalysisWords = false
                            writingNoSolutionWords()
                        }
                    })
                })
            } else {
                return new Promise((resolve, reject) => {
                    // console.log('')
                    // 把keyword里面可能存在的sceneList、abilityList、productList进行遍历，把里面每一项的description、key掏出来组成一个新的数组，用于去重
                    const descriptionAndkeyArr = [
                        ...(data.AIData.keyword.sceneList || []).map(({ description, key }) => ({ description, key })),
                        ...(data.AIData.keyword.abilityList || []).map(({ description, key }) => ({ description, key })),
                        ...(data.AIData.keyword.productList || []).map(({ description, key }) => ({ description, key }))
                    ];
                    // console.log('去重前的数组', descriptionAndkeyArr)
                    // 把descriptionAndkeyArr数组进行去重
                    const seen = new Set();
                    const uniqueDescriptionAndkeyArr = descriptionAndkeyArr.filter(item => {
                        const identifier = `${item.description}|${item.key}`;
                        return seen.has(identifier) ? false : seen.add(identifier);
                    });
                    data.peopleSaidAbilityList = uniqueDescriptionAndkeyArr
                    console.log('人说的去重后的数组', uniqueDescriptionAndkeyArr)
                    // 单召回接口看有没有方案
                    getRetrieveList({
                        knowledgeType: 1,
                        description: data.AIData.keyword.solutionList[0].key,
                        topK: 10,
                        name: data.AIData.keyword.solutionList[0].key,
                        type: 1,
                    }).then((res) => {
                        data.solutionStatus = '选方案中'
                        if (res.data.dataList.length !== 0) {
                            let hasItemWithIsSensitiveZero = false;
                            // let firstIsSensitiveZeroIndex = -1; // 第一个不是敏感方案的方案索引，默认索引为-1，表示未找到
                            // 找到第一个非敏感方案才可以去定制
                            if (res.data.dataList && Array.isArray(res.data.dataList)) {
                                hasItemWithIsSensitiveZero = res.data.dataList.some(item => item && item.isSensitive === 0);
                                // 寻找第一个不是敏感方案的方案索引
                                // if (hasItemWithIsSensitiveZero) {
                                //     firstIsSensitiveZeroIndex = res.data.dataList.findIndex(item => item && item.isSensitive === 0);
                                // }
                            }
                            // // 你可以在这里使用 firstIsSensitiveZeroIndex，例如：
                            // console.log('第一个isSensitive为0的对象的索引是:', firstIsSensitiveZeroIndex);

                            // 有非敏感方案
                            if (hasItemWithIsSensitiveZero) {

                                // 剔除isSensitive不为0的项组成新数组
                                const filteredDataList = res.data.dataList.filter(item => item && item.isSensitive === 0);
                                console.log('所有不是敏感方案的方案', filteredDataList);
                                data.showSolutionList = filteredDataList
                                typeWriterPromptChooseSolution()

                            } else {
                                // 没有非敏感方案,即没搜到方案
                                emit('clearPPTList')
                                data.nowSolutionId = null
                                data.goHistoryData.hasSolution = false
                                data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
                                // 老的没有搜到方案的情况
                                data.showAnalysisWords = false
                                writingNoSolutionWords()
                                // newWritingNoSolution()
                            }
                        } else {
                            // 没搜到方案
                            emit('clearPPTList')
                            data.nowSolutionId = null
                            data.goHistoryData.hasSolution = false
                            data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
                            // 老的没有搜到方案的情况
                            data.showAnalysisWords = false

                            writingNoSolutionWords()
                            // newWritingNoSolution()
                        }
                    })
                })
            }
        }
        // 当用户选择了方案时，接下来处理
        const getPersonChooseSolutionData = (item) => {
            data.isAIWriting = true
            emit("continueScroll")
            console.log('itemxxxxxxxxx', item)
            emit('changeRightModuleType', 'PPT')
            emit('clearPPTList')
            emit('contorlAddingPic', 'true')
            item.specialTitle = '方案'
            item.customizedType = 1
            data.hasSolution = true
            data.goCustomizedList.push(item)
            data.nowSolutionDetail = item//存当前方案的详情
            data.nowSolutionId = item.id
            data.nowSolutionName = item.name
            data.nowSolutionDescription = item.description
            data.solutionOverviewText5 = item.description
            data.firstTitleText = '已分析用户需求，即将开始' + data.AIData.keyword.solutionList[0].key + '方案的生成：'
            data.solutionOverviewText2 = '小麟正在为' + data.AIData.keyword.solutionList[0].key + '方案生成方案概述...'
            // 存数据用于回显
            data.goHistoryData.hasSolution = true
            data.goHistoryData.peopleSaySolutionName = data.AIData.keyword.solutionList[0].key
            data.goHistoryData.solutionName = item.name
            data.goHistoryData.solutionOverview = item.description
            if (item.slideUrls.length !== 0) {
                // PPT的
                emit('addPPTBigTitle', '方案概述')
                data.goHistoryData.PPTList.push({
                    bigTitle: '方案概述'
                })
                let arr = []
                for (let i = 1; i < item.slideUrls.length; i++) {
                    arr.push({
                        img: item.slideUrls[i]
                    })
                    data.goHistoryData.PPTList.push({
                        img: item.slideUrls[i]
                    })
                }
                emit('addPPT', arr)

                // for (let i of item.slideUrls) {
                //     data.goHistoryData.PPTList.push({
                //         img: i
                //     })
                // }
            }
            data.showGuideWords = false
            typeWriterFirstTitleText()
            if (item.sceneInfoList.length !== 0) {
                for (let i of item.sceneInfoList) {
                    if (i.name) {
                        data.solutionSceneList.push({
                            name: i.name,
                            description: i.descShow,
                            slideUrls: i.slideUrls,
                            id: i.id,
                            specialTitle: '场景'
                        })
                    }
                    i.specialTitle = "场景"
                    // 把方案自带的场景数据存到goCustomizedList中
                    data.goCustomizedList.push(i)
                }
            }
            if (item.caseInfoList.length !== 0) {
                for (let i of item.caseInfoList) {
                    if (i.caseName) {
                        data.solutionCaseList.push({
                            name: i.caseName,
                            description: i.descShow,
                            slideUrls: i.slideUrls,
                            id: i.id,
                            specialTitle: '案例'
                        })
                    }
                    i.specialTitle = "案例"
                    // 把方案自带的案例数据存到goCustomizedList中
                    // data.goCustomizedList.push(i)
                }
            }
            // emit('clearPPTList')
            // emit('contorlAddingPic', 'true')
        }
        // 当意图为检索的时候，处理数据
        const getAISearchData = () => {
            return new Promise((resolve, reject) => {

                const descriptionAndkeyArr = [
                    ...(data.AIData.keyword.sceneList || []).map(({ description, key }) => ({ description, key })),
                    ...(data.AIData.keyword.abilityList || []).map(({ description, key }) => ({ description, key })),
                    ...(data.AIData.keyword.productList || []).map(({ description, key }) => ({ description, key }))
                ];
                // console.log('去重前的数组', descriptionAndkeyArr)
                // 把descriptionAndkeyArr数组进行去重
                const seen = new Set();
                const uniqueDescriptionAndkeyArr = descriptionAndkeyArr.filter(item => {
                    const identifier = `${item.description}|${item.key}`;
                    return seen.has(identifier) ? false : seen.add(identifier);
                });
                // console.log('去重后的数组', uniqueDescriptionAndkeyArr)
                const promises = [];
                // 语义识别到了要检索方案
                if (data.AIData.keyword.solutionList) {
                    for (const item of data.AIData.keyword.solutionList) {
                        const promise = getRetrieveList({
                            knowledgeType: 1,
                            description: item.description,
                            topK: 10,
                            name: item.key,
                        }).then((res) => {
                            // res.data.dataList.forEach(j => {
                            //     data.goSearchPageList.solutionList.push(j);
                            // });
                            data.goSearchPageList.solutionList.push({
                                key: item.key,
                                knowledgeType: '方案',
                                solution: res.data.dataList,
                                priority: res.data.priority,
                            });
                            // console.log('添加方案后的列表', data.goSearchPageList);
                        });
                        promises.push(promise);
                    }
                }
                // 2025/5/26新增逻辑，给什么就搜什么，如需修改，注释以下的能力/场景/产品检索
                // 能力
                if (data.AIData.keyword.abilityList) {
                    for (const item of data.AIData.keyword.abilityList) {
                        const promise = getRetrieveList({
                            knowledgeType: 2,//能力
                            description: item.description,
                            topK: 10,
                            name: item.key,
                        }).then((res) => {
                            // res.data.dataList.forEach(j => {
                            //     data.goSearchPageList.solutionList.push(j);
                            // });
                            data.goSearchPageList.abilityList.push({
                                key: item.key,
                                knowledgeType: '能力',
                                ability: res.data.dataList,
                                priority: res.data.priority,
                            });
                            // console.log('添加方案后的列表', data.goSearchPageList);
                        });
                        promises.push(promise);
                    }
                }
                // 场景
                if (data.AIData.keyword.sceneList) {
                    for (const item of data.AIData.keyword.sceneList) {
                        const promise = getRetrieveList({
                            knowledgeType: 3,//场景
                            description: item.description,
                            topK: 10,
                            name: item.key,
                        }).then((res) => {
                            // res.data.dataList.forEach(j => {
                            //     data.goSearchPageList.solutionList.push(j);
                            // });
                            data.goSearchPageList.sceneList.push({
                                key: item.key,
                                knowledgeType: '场景',
                                scene: res.data.dataList,
                                priority: res.data.priority,
                            });
                            // console.log('添加方案后的列表', data.goSearchPageList);
                        });
                        promises.push(promise);
                    }
                }
                // 产品
                if (data.AIData.keyword.productList) {
                    for (const item of data.AIData.keyword.productList) {
                        const promise = getRetrieveList({
                            knowledgeType: 5,//产品
                            description: item.description,
                            topK: 10,
                            name: item.key,
                        }).then((res) => {
                            // res.data.dataList.forEach(j => {
                            //     data.goSearchPageList.solutionList.push(j);
                            // });
                            data.goSearchPageList.productList.push({
                                key: item.key,
                                knowledgeType: '产品',
                                product: res.data.dataList,
                                priority: res.data.priority,
                            });
                            // console.log('添加产品后的列表', data.goSearchPageList);
                        });
                        promises.push(promise);
                    }
                }
                // 语义识别有产品包
                if (data.AIData.keyword.productPackageList) {
                    for (const item of data.AIData.keyword.productPackageList) {
                        const promise = getRetrieveList({
                            knowledgeType: 4,
                            description: item.description,
                            topK: 10,
                            name: item.key,
                        }).then((res) => {
                            // res.data.dataList.forEach(j => {
                            //     data.goSearchPageList.productPackageList.push(j);
                            // });
                            data.goSearchPageList.productPackageList.push({
                                key: item.key,
                                knowledgeType: '产品包',
                                productBag: res.data.dataList,
                                priority: res.data.priority,
                            });
                            // console.log('添加产品包后的列表', data.goSearchPageList);
                        });
                        promises.push(promise);
                    }
                }
                // 2025/5/26注释之前的逻辑，现在是检索给什么就搜什么
                // // 只有产品包productPackageList没有方案solutionList的时候，只搜产品
                // if (data.AIData.keyword.productPackageList && !data.AIData.keyword.solutionList) {
                //     // console.log('sssssszzzzzz')
                //     if (uniqueDescriptionAndkeyArr.length !== 0) {
                //         for (const item of uniqueDescriptionAndkeyArr) {
                //             const promise = getRetrieveList({
                //                 knowledgeType: 5,//产品
                //                 description: item.description,
                //                 topK: 10,
                //                 name: item.key,
                //             }).then((res) => {
                //                 // res.data.dataList.forEach(j => {
                //                 //     data.goSearchPageList.productList.push(j);
                //                 // });
                //                 data.goSearchPageList.productList.push({
                //                     key: item.key,
                //                     knowledgeType: '产品',
                //                     product: res.data.dataList,
                //                     priority: res.data.priority,
                //                 });
                //                 // console.log('添加产品后的列表', data.goSearchPageList);
                //             });
                //             promises.push(promise);
                //         }
                //     }
                // } else {
                //     if (uniqueDescriptionAndkeyArr.length !== 0) {
                //         for (const item of uniqueDescriptionAndkeyArr) {
                //             const promise = getRetrieveList({
                //                 knowledgeType: 2,//能力
                //                 description: item.description,
                //                 topK: 10,
                //                 name: item.key,
                //             }).then((res) => {
                //                 // res.data.dataList.forEach(j => {
                //                 //     data.goSearchPageList.abilityList.push(j);
                //                 // });
                //                 data.goSearchPageList.abilityList.push({
                //                     key: item.key,
                //                     knowledgeType: '能力',
                //                     ability: res.data.dataList,
                //                     priority: res.data.priority,
                //                 });
                //                 // console.log('添加能力后的列表', data.goSearchPageList);
                //             });
                //             promises.push(promise);
                //         }
                //         for (const item of uniqueDescriptionAndkeyArr) {
                //             const promise = getRetrieveList({
                //                 knowledgeType: 3,//场景
                //                 description: item.description,
                //                 topK: 10,
                //                 name: item.key,
                //             }).then((res) => {
                //                 // res.data.dataList.forEach(j => {
                //                 //     data.goSearchPageList.sceneList.push(j);
                //                 // });
                //                 data.goSearchPageList.sceneList.push({
                //                     key: item.key,
                //                     knowledgeType: '场景',
                //                     scene: res.data.dataList,
                //                     priority: res.data.priority,
                //                 });
                //                 // console.log('添加场景后的列表', data.goSearchPageList);
                //             });
                //             promises.push(promise);
                //         }
                //         for (const item of uniqueDescriptionAndkeyArr) {
                //             const promise = getRetrieveList({
                //                 knowledgeType: 5,//产品
                //                 description: item.description,
                //                 topK: 10,
                //                 name: item.key,
                //             }).then((res) => {
                //                 // res.data.dataList.forEach(j => {
                //                 //     data.goSearchPageList.productList.push(j);
                //                 // });
                //                 data.goSearchPageList.productList.push({
                //                     key: item.key,
                //                     knowledgeType: '产品',
                //                     product: res.data.dataList,
                //                     priority: res.data.priority,
                //                 });
                //                 // console.log('添加产品后的列表', data.goSearchPageList);
                //             });
                //             promises.push(promise);
                //         }
                //     }
                // }
                data.goHistoryData.goSearchPageList = data.goSearchPageList

                // 等待所有异步操作完成
                Promise.all(promises)
                    .then(() => {
                        // console.log('最终列表', data.goSearchPageList)
                        localStorage.setItem("goSearchPageList", JSON.stringify(data.goSearchPageList));
                        // 调用typeWriterAISearchOver，并确保它执行完毕
                        return typeWriterAISearchOver();
                    })
                    .then(() => resolve()) // typeWriterAISearchOver完成后resolve
                    .catch(err => reject(err));
            });
        }
        const jumpToSearch = (list) => {
            console.log('list', list)
            localStorage.setItem("goSearchPageList", JSON.stringify(list));
            localStorage.setItem('goCustomizedSessionId', data.sessionId)
            // Router.push({
            //     path: "/newProject/newProject",
            //     query: {
            //         type: 6
            //     }
            // });
            let href = window.location.origin + '/#/newProject/newProject?type=6'
            console.log('href', href)
            window.open(href, '_blank')
        }
        // ☆☆☆☆☆☆ HDICT的跳转 ↓ ☆☆☆☆☆☆
        // 检索的HDICT，处理数据
        const getAISearchHDICTData = () => {
            console.log('data.AIData.keyword', data.AIData.keyword)
            if (data.AIData.keyword.houseType.length + data.AIData.keyword.product.length + data.AIData.keyword.scene.length == 0) {
                //说明没有检索到任何东西
                data.goHistoryData.isHDICTSearched = false
                HDICTNoData()
            } else {
                data.goHistoryData.isHDICTSearched = true
                if (data.AIData.keyword.houseType.length !== 0) {
                    // 优先户型，有户型则检索户型
                    data.HDICTJumpType = 1
                    data.HDICTJumpKeyword = data.AIData.keyword.houseType[0]
                } else {
                    if (data.AIData.keyword.scene.length !== 0) {
                        // 其次优先场景，有场景则检索场景
                        data.HDICTJumpType = 2
                        data.HDICTJumpKeyword = data.AIData.keyword.scene[0]

                    } else {
                        // 没有户型和场景，则检索产品
                        data.HDICTJumpType = 3
                        data.HDICTJumpKeyword = data.AIData.keyword.product[0]

                    }
                }
                console.log('data.HDICTJumpType', data.HDICTJumpType)
                console.log('data.HDICTJumpKeyword', data.HDICTJumpKeyword)
                data.goHistoryData.HDICTJumpType = data.HDICTJumpType
                data.goHistoryData.HDICTJumpKeyword = data.HDICTJumpKeyword
                // 存历史记录
                storeChatResult({
                    sessionId: data.sessionId,
                    aimDescription: JSON.stringify(data.goHistoryData)
                }).then((res) => {
                    console.log('res', res)
                    data.chatId = res.data
                    data.goHistoryData.chatId = res.data
                })
                setTimeout(() => {
                    typeWriterHDICTJumping()
                }, 1000)
            }
        }
        // 检索HDICT，但是没有搜到任何东西
        const HDICTNoData = () => {
            storeChatResult({
                sessionId: data.sessionId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
                data.chatId = res.data
                data.goHistoryData.chatId = res.data
            })
            let HDICTNoDataText = null
            if (data.AIData.busOperate == '定制') {
                HDICTNoDataText = '我已明确您想定制一个HDICT方案，但是您的描述有点模糊，小麟未能理解清楚，可以麻烦您详细的描述您的需求吗，比如您的户型，有哪些房间，要什么产品？'
            } else if (data.AIData.busOperate == '检索') {
                HDICTNoDataText = '我已明确您想检索一个HDICT方案，但是您的描述有点模糊，小麟未能理解清楚，可以麻烦您详细的描述您的需求吗，比如您的户型，有哪些房间，要什么产品？'
            }
            if (data.continueProcess) {
                return new Promise((resolve) => {
                    const write = () => {
                        if (data.HDICTSearchNoDataIndex < HDICTNoDataText.length) {
                            data.writingHDICTSearchNoDataText += HDICTNoDataText.charAt(data.HDICTSearchNoDataIndex);
                            data.HDICTSearchNoDataIndex++;
                            setTimeout(write, 20);
                        } else {
                            resolve();
                            writingOver()
                        }
                    };
                    write();
                });
            }
        }
        // 小麟正在为您跳转至HDICT检索页面......
        const typeWriterHDICTJumping = () => {
            data.HDICTJumpingText = '小麟正在为您跳转至HDICT检索页面......'
            return new Promise((resolve) => {
                const write = () => {
                    if (data.HDICTJumpingIndex < data.HDICTJumpingText.length) {
                        data.writingHDICTJumpingText += data.HDICTJumpingText.charAt(data.HDICTJumpingIndex);
                        data.HDICTJumpingIndex++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        setTimeout(() => {
                            jumpToXingYeHuiJieSearched()
                        }, 500)
                        writingOver();
                    }
                };
                write();
            });
        };
        // 跳转去星邺汇捷的检索页面
        const jumpToXingYeHuiJieSearched = () => {
            // 测试：http://36.138.46.45:8081
            // 生产：https://home.jsisi.cn:8099
            getCurTab.linkUrl = `${embedUrl}/hdict/index` + `?token=${localStorage.getItem("token")}` + `&from=ai` + `&keyword=${data.HDICTJumpKeyword}` + `&type=${data.HDICTJumpType}`;
            Router.push({
                name: "thirdView",
            });
            return
        }
        // 历史记录里的HDICT检索的跳转
        const jumpToHDICT = (keyword, type) => {
            // 测试：http:/ / 36.138.46.45: 8081
            // 生产：https://home.jsisi.cn:8099
            getCurTab.linkUrl = `${embedUrl}/hdict/index` + `?token=${localStorage.getItem("token")}` + `&from=ai` + `&keyword=${keyword}` + `&type=${type}`;
            Router.push({
                name: "thirdView",
            });
            return
        }
        // 小麟暂不支持HDICT的定制
        const typeWriterCantCustomizeHDICT = () => {
            data.cantCustomizeHDICTText = '小麟目前还在学习如何[定制]智慧家庭方案，所以现在小麟只能为您检索智慧家庭方案'
            return new Promise((resolve) => {
                const write = () => {
                    if (data.cantCustomizeHDICTIndex < data.cantCustomizeHDICTText.length) {
                        data.writingCantCustomizeHDICTText += data.cantCustomizeHDICTText.charAt(data.cantCustomizeHDICTIndex);
                        data.cantCustomizeHDICTIndex++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        writingOver();
                    }
                };
                write();
            });
        }
        //HDICT的定制↓
        // 好的，我将为您直接定制一份个体民宿场景包
        const typeWriterHDICTCustomizedText1 = () => {
            let text = ''
            if (data.isJiangSu) {
                text = '好的，我将为您直接定制一份智慧家庭方案'
            } else {
                text = '小麟在为您定制时，发现您的账号权限目前只能使用行业与商客条线的功能，暂时不能使用HDICT功能。很抱歉这次没能帮到您！'
            }
            return new Promise((resolve) => {
                const write = () => {
                    if (data.HDICTText1Index < text.length) {
                        data.writingHDICTText1 += text.charAt(data.HDICTText1Index);
                        data.HDICTText1Index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        if (data.isJiangSu) {
                            setTimeout(() => {
                                if (data.continueProcess) {
                                    typeWriterHDICTCustomizedText2()
                                }
                            }, 600)
                        } else {
                            storeChatResult({
                                sessionId: data.sessionId,
                                aimDescription: JSON.stringify(data.goHistoryData)
                            }).then((res) => {
                                console.log('res', res)
                                data.chatId = res.data
                                data.goHistoryData.chatId = res.data
                            })
                            writingOver()
                        }

                    }
                };
                write();
            });
        }
        // 您的智慧家庭方案中已包含如下内容:
        const typeWriterHDICTCustomizedText2 = () => {
            let text = '您的智慧家庭方案中已包含如下内容：'
            return new Promise((resolve) => {
                const write = () => {
                    if (data.HDICTText2Index < text.length) {
                        data.writingHDICTText2 += text.charAt(data.HDICTText2Index);
                        data.HDICTText2Index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        if (data.continueProcess) {
                            handleHouseTypeCustomizedData()
                        }
                    }
                };
                write();
            });
        }
        // 处理户型定制数据
        const handleHouseTypeCustomizedData = async () => {
            if (data.AIData.keyword.houseType && data.AIData.keyword.houseType.length !== 0) {
                data.houseTypeList = []
                const promises = data.AIData.keyword.houseType.map(async (i) => {
                    let houseTypeId = null
                    let houseTypeName = null
                    let brandName = null
                    let brandId = null

                    // 等待户型接口调用完成
                    const houseTypeRes = await getRetrieveList({
                        knowledgeType: 10,//10，查户型
                        description: i.name,
                        topK: 10,
                        name: i.name,
                        type: 1,
                    })
                    console.log('res', houseTypeRes)
                    if (houseTypeRes.data.dataList && houseTypeRes.data.dataList.length !== 0) {
                        houseTypeId = houseTypeRes.data.dataList[0].id
                        houseTypeName = houseTypeRes.data.dataList[0].name
                        // HDICT的抽屉
                        if (!data.isSBchange) {
                            emit('showHDICTBox', {
                                data: houseTypeRes.data.dataList[0],
                                type: 'houseType'
                            })
                            data.goHistoryData.HDICTList.push({
                                data: houseTypeRes.data.dataList[0],
                                type: 'houseType'
                            })
                        }
                    }

                    // 等待品牌接口调用完成
                    const brandRes = await getRetrieveList({
                        knowledgeType: 12,//12，查品牌
                        description: i.brand,
                        topK: 10,
                        name: i.brand,
                        type: 1,
                    })
                    console.log('res', brandRes)
                    if (brandRes.data.dataList && brandRes.data.dataList.length !== 0) {
                        data.houseTypeBrand = brandRes.data.dataList[0].name
                        brandName = brandRes.data.dataList[0].name
                        brandId = brandRes.data.dataList[0].id
                    }

                    // 返回处理后的数据
                    return {
                        houseTypeId,
                        houseTypeName,
                        brandName,
                        brandId,
                        originalItem: i
                    }
                })

                // 等待所有接口调用完成
                const results = await Promise.all(promises)

                // 处理所有结果
                results.forEach(result => {
                    if (result.houseTypeId) {
                        data.houseTypeList.push({
                            id: result.houseTypeId,
                            name: result.houseTypeName,
                            brandid: result.brandId,
                            brandname: result.brandName,
                        })
                    }
                    data.writingHouseTypeList.push({
                        id: result.houseTypeId ? result.houseTypeId : null,
                        name: result.houseTypeName ? result.houseTypeName : '【' + result.originalItem.name + '】：平台里好像还没有' + result.originalItem.name + '相关户型，我们未来将会积极补全！',
                        brandid: result.brandId,
                        brandname: result.brandName,
                    })
                })

                data.goHistoryData.HDICTCustomized.writingHouseTypeList = data.writingHouseTypeList
                typeWriterHouseType1()
            } else {
                if (data.continueProcess) {
                    handleSceneCustomizedData()
                }
            }
        }
        // 户型的打字机
        const typeWriterHouseType1 = () => {
            return new Promise((resolve) => {
                const write = async () => {
                    if (data.houseType1Index < data.houseType1Text.length) {
                        data.writingHouseType1Text += data.houseType1Text.charAt(data.houseType1Index);
                        data.houseType1Index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        if (data.continueProcess) {
                            await typeWriterHouseType2()
                        }
                        if (data.continueProcess) {
                            await handleSceneCustomizedData()
                        }
                    }
                };
                write();
            });
        }
        // 户型的列表打字机
        const typeWriterHouseType2 = async () => {
            for (const type of data.writingHouseTypeList) {
                const newItem = reactive({
                    ...type,
                    displayText: '',
                });
                data.showHouseTypeList.push(newItem);
                if (data.continueProcess) {
                    await processAddHouseType(newItem);
                }
            }
        }
        const processAddHouseType = async (item) => {
            if (data.continueProcess) {
                //找到了产品
                item.searched = '已搜到'
                await typeWrite(item, 'displayText', item.name);
            }
        }
        // 处理场景定制数据
        const handleSceneCustomizedData = async () => {
            if (data.AIData.keyword.scene && data.AIData.keyword.scene.length !== 0) {
                data.HDICTSceneList = []
                const promises = data.AIData.keyword.scene.map(async (i) => {
                    let brandName = null
                    let brandId = null

                    // 等待场景接口调用完成
                    const sceneRes = await getRetrieveList({
                        knowledgeType: 9,//9，查HDICT场景
                        description: i.name,
                        topK: 10,
                        name: i.name,
                        type: 1,
                    })
                    console.log(sceneRes)

                    if (sceneRes.data.dataList && sceneRes.data.dataList.length !== 0) {
                        // 如果有品牌信息，等待品牌接口调用完成
                        if (i.brand) {
                            try {
                                const brandRes = await getRetrieveList({
                                    knowledgeType: 12,//10，查品牌
                                    description: i.brand,
                                    topK: 10,
                                    name: i.brand,
                                    type: 1,
                                })
                                console.log('res品牌', brandRes)
                                if (brandRes.data.dataList && brandRes.data.dataList.length !== 0) {
                                    data.sceneBrand = brandRes.data.dataList[0].name
                                    brandName = brandRes.data.dataList[0].name
                                    brandId = brandRes.data.dataList[0].id
                                }
                            } catch (error) {
                                console.error('获取品牌信息失败:', error)
                            }
                        }

                        // 返回处理后的数据
                        return {
                            id: sceneRes.data.dataList[0].id,
                            name: sceneRes.data.dataList[0].name,
                            brandid: brandId,
                            brandname: brandName,
                            data: sceneRes.data.dataList[0],
                            found: true
                        }
                    } else {
                        // 返回未找到的数据
                        return {
                            id: null,
                            name: '【' + i.name + '】：平台里好像还没有' + i.name + '相关场景，我们未来将会积极补全！',
                            brandid: null,
                            brandname: null,
                            found: false
                        }
                    }
                })

                // 等待所有接口调用完成
                const results = await Promise.all(promises)

                // 处理所有结果
                results.forEach(result => {
                    if (result.found) {
                        data.HDICTSceneList.push({
                            id: result.id,
                            name: result.name,
                            brandid: result.brandid,
                            brandname: result.brandname,
                        })
                        data.writingHDICTSceneList.push({
                            id: result.id,
                            name: result.name,
                            brandid: result.brandid,
                            brandname: result.brandname,
                        })
                        // HDICT的抽屉
                        if (!data.isSBchange) {
                            emit('showHDICTBox', {
                                data: result.data,
                                type: 'scene'
                            })
                            data.goHistoryData.HDICTList.push({
                                data: result.data,
                                type: 'scene'
                            })
                        }
                    } else {
                        data.writingHDICTSceneList.push({
                            id: result.id,
                            name: result.name,
                            brandid: result.brandid,
                            brandname: result.brandname,
                        })
                    }
                })

                data.goHistoryData.HDICTCustomized.writingHDICTSceneList = data.writingHDICTSceneList
                if (data.writingHDICTSceneList.length !== 0) {
                    if (data.writingHouseTypeList.length !== 0) {
                        data.scene1Text = '二、场景'
                    } else {
                        data.scene1Text = '一、场景'
                    }
                    data.goHistoryData.HDICTCustomized.HDICTSceneWord = data.scene1Text
                    await new Promise(resolve => {
                        resolve()
                        typeWriterScene1()
                    })
                } else {
                    if (data.continueProcess) {
                        handleProductCustomizedData()
                    }
                }
            } else {
                if (data.continueProcess) {
                    handleProductCustomizedData()
                }
            }
        }
        // 场景的打字机
        const typeWriterScene1 = () => {
            return new Promise((resolve) => {
                const write = async () => {
                    if (data.scene1Index < data.scene1Text.length) {
                        data.writingScene1Text += data.scene1Text.charAt(data.scene1Index);
                        data.scene1Index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        if (data.continueProcess) {
                            await typeWriterScene2()
                        }
                        if (data.continueProcess) {
                            await handleProductCustomizedData()
                        }
                    }
                };
                write();
            });
        }
        // 场景列表的打字机
        const typeWriterScene2 = async () => {
            for (const type of data.writingHDICTSceneList) {
                const newItem = reactive({
                    ...type,
                    displayText: '',
                });
                data.showHDICTSceneList.push(newItem);
                if (data.continueProcess) {
                    await processAddHDICTScene(newItem);
                }
            }
        }
        const processAddHDICTScene = async (item) => {
            if (data.continueProcess) {
                //找到了产品
                item.searched = '已搜到'
                await typeWrite(item, 'displayText', item.name);
            }
        }
        // 处理产品定制数据
        const handleProductCustomizedData = async () => {
            if (data.AIData.keyword.product && data.AIData.keyword.product.length !== 0) {
                data.HDICTProductList = []
                console.log('data.AIData.keyword.product111111111', data.AIData)
                let brand = data.houseTypeBrand ? data.houseTypeBrand : data.sceneBrand
                const promises = data.AIData.keyword.product.map(i => {
                    return getRetrieveList({
                        knowledgeType: 11,//11，查HDICT产品
                        description: i.name + brand,
                        topK: 10,
                        name: i.name + brand,
                        type: 1,
                    }).then((res) => {
                        console.log(res)
                        if (res.data.dataList && res.data.dataList.length !== 0) {
                            data.HDICTProductList.push({
                                id: res.data.dataList[0].id,
                                name: res.data.dataList[0].name,
                                num: Number(i.num)
                            })
                            data.writingHDICTProductList.push({
                                id: res.data.dataList[0].id,
                                name: res.data.dataList[0].name,
                                num: Number(i.num)
                            })
                            // HDICT的抽屉
                            if (!data.isSBchange) {
                                emit('showHDICTBox', {
                                    data: res.data.dataList[0],
                                    type: 'product'
                                })
                                data.goHistoryData.HDICTList.push({
                                    data: res.data.dataList[0],
                                    type: 'product'
                                })
                            }
                        } else {
                            data.writingHDICTProductList.push({
                                id: null,
                                name: '【' + i.name + '】：平台里好像还没有' + i.name + '相关产品，我们未来将会积极补全！',
                                num: Number(i.num)
                            })
                        }
                    })
                })
                await Promise.all(promises)
                data.goHistoryData.HDICTCustomized.writingHDICTProductList = data.writingHDICTProductList
                if (data.writingHDICTProductList.length !== 0) {
                    if (data.writingHouseTypeList.length !== 0 && data.writingHDICTSceneList.length !== 0) {
                        data.HDICTProductText = '三、产品'
                    } else if (data.writingHouseTypeList.length !== 0 || data.writingHDICTSceneList.length !== 0) {
                        data.HDICTProductText = '二、产品'
                    } else if (data.writingHouseTypeList.length == 0 && data.writingHDICTSceneList.length == 0) {
                        data.HDICTProductText = '一、产品'
                    }
                    data.goHistoryData.HDICTCustomized.HDICTProductWord = data.HDICTProductText
                    await new Promise(resolve => {
                        typeWriterHDICTProduct1()
                        setTimeout(() => {
                            resolve()
                        }, 500)
                    })


                } else {
                    if (data.continueProcess) {
                        if (data.houseTypeList.length + data.HDICTSceneList.length + data.HDICTProductList.length !== 0) {
                            data.goHistoryData.HDICTCustomized.isAISearchThing = true
                            // 进入总结
                            HDICTCustomizedSummary()
                        } else {
                            // 说明搜了，但是没有搜到东西
                            data.goHistoryData.HDICTCustomized.isAISearchThing = false
                            HDICTCustomizedNoData()
                        }
                    }
                }
            } else {
                if (data.continueProcess) {
                    if (data.houseTypeList.length + data.HDICTSceneList.length + data.HDICTProductList.length !== 0) {
                        data.goHistoryData.HDICTCustomized.isAISearchThing = true
                        // 进入总结
                        HDICTCustomizedSummary()
                    } else {
                        // 说明搜了，但是没有搜到东西
                        data.goHistoryData.HDICTCustomized.isAISearchThing = false
                        HDICTCustomizedNoData()
                    }

                }
            }
        }
        // 三、产品的打字机
        const typeWriterHDICTProduct1 = async () => {
            return new Promise((resolve) => {
                const write = async () => {
                    if (data.product1Index < data.HDICTProductText.length) {
                        data.writingProduct1Text += data.HDICTProductText.charAt(data.product1Index);
                        data.product1Index++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        if (data.continueProcess) {
                            await typeWriterHDICTProduct2()
                            if (data.continueProcess) {
                                if (data.houseTypeList.length + data.HDICTSceneList.length + data.HDICTProductList.length !== 0) {
                                    data.goHistoryData.HDICTCustomized.isAISearchThing = true
                                    // 进入总结
                                    await HDICTCustomizedSummary()
                                } else {
                                    // 说明搜了，但是没有搜到东西
                                    data.goHistoryData.HDICTCustomized.isAISearchThing = false
                                    await HDICTCustomizedNoData()
                                }
                            }
                        }
                    }
                };
                write();
            });
        }
        // 产品数组的打字机
        const typeWriterHDICTProduct2 = async () => {
            for (const type of data.writingHDICTProductList) {
                const newItem = reactive({
                    ...type,
                    displayText: '',
                });
                data.showHDICTProductList.push(newItem);
                if (data.continueProcess) {
                    await processAddHDICTProduct(newItem);
                }
            }
        }
        const processAddHDICTProduct = async (item) => {
            if (data.continueProcess) {
                //找到了产品
                item.searched = '已搜到'
                await typeWrite(item, 'displayText', item.name);
            }
        }
        // 定制HDICT时，去搜了，但是都没有搜到东西
        const HDICTCustomizedNoData = () => {
            if (!data.isSBchange) {
                emit('contorlAddingHDICT', 'false')
            }
            console.log('data.goHistoryDataxxxxxxxx', data.goHistoryData)
            storeChatResult({
                sessionId: data.sessionId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
                data.chatId = res.data
                data.goHistoryData.chatId = res.data
            })
            let text = '很抱歉，小麟没有找到您想要的内容，小麟也将继续学习，争取早日为您找到您想要的内容！'
            return new Promise((resolve) => {
                const write = () => {
                    if (data.HDICTCustomizedNoDataIndex < text.length) {
                        data.writingHDICTCustomizedNoDataText += text.charAt(data.HDICTCustomizedNoDataIndex);
                        data.HDICTCustomizedNoDataIndex++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        writingOver()
                    }
                };
                write();
            });
        }
        // HDICT定制的总结语：您的智慧家庭方案已经定制完成，是否查看
        const HDICTCustomizedSummary = () => {
            // HDICT的抽屉
            if (!data.isSBchange) {
                emit('contorlAddingHDICT', 'false')
            }
            data.HDICTCustomizedResult.houseType = data.houseTypeList
            data.HDICTCustomizedResult.scene = data.HDICTSceneList
            data.HDICTCustomizedResult.product = data.HDICTProductList
            data.goHistoryData.HDICTCustomized.jumpResult = data.HDICTCustomizedResult
            console.log('data.HDICTCustomizedResult1111111111', data.HDICTCustomizedResult)
            storeChatResult({
                sessionId: data.sessionId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
                data.chatId = res.data
                data.goHistoryData.chatId = res.data
            })
            let text = '您的智慧家庭方案已经定制完成，是否查看？'
            return new Promise((resolve) => {
                const write = () => {
                    if (data.HDICTCustomizedSummaryIndex < text.length) {
                        data.writingHDICTCustomizedSummary += text.charAt(data.HDICTCustomizedSummaryIndex);
                        data.HDICTCustomizedSummaryIndex++;
                        setTimeout(write, 20);
                    } else {
                        resolve();
                        data.showHDICTCustomizedBtn = true
                        writingOver()
                    }
                };
                write();
            });
        }
        // 点击确定跳转去星邺汇捷的定制页面
        const goHDICTCustomizedBtn = () => {
            console.log('data.HDICTCustomizedResult', data.HDICTCustomizedResult)
            console.log('data.goHistoryData.HDICTCustomized.jumpResult', data.goHistoryData.HDICTCustomized.jumpResult)
            data.goHDICTCustomizedBtnDisabled = true
            data.showGoHDICTCustomizedProductLoading = false
            // 测试：http://36.138.46.45:8081/hdict/AICustomize
            // 生产：https://home.jsisi.cn:8099/hdict/AICustomize
            const baseUrl = `${embedUrl}/hdict/AICustomize`
            // const url = `${baseUrl}?token=${localStorage.getItem('token')}&keyword=${encodeURIComponent(JSON.stringify(data.HDICTCustomizedResult))}`
            // window.open(url)
            getCurTab.linkUrl = `${baseUrl}?token=${localStorage.getItem('token')}&keyword=${encodeURIComponent(JSON.stringify(data.HDICTCustomizedResult))}`;
            Router.push({
                name: "thirdView",
            });

            setTimeout(() => {
                data.goHDICTCustomizedBtnDisabled = false
                data.showGoHDICTCustomizedProductLoading = true
            }, 1000)
        }
        // 历史记录里HDICT定制的跳转
        const HDICTCustomizedJump = (result) => {
            data.HDICTCustomizedHistoryLoading = false
            console.log('result', result);
            // 测试：http://36.138.46.45:8081/hdict/AICustomize
            // 生产：https://home.jsisi.cn:8099/hdict/AICustomize
            const baseUrl = `${embedUrl}/hdict/AICustomize`
            // const url = `${baseUrl}?token=${localStorage.getItem('token')}&keyword=${encodeURIComponent(JSON.stringify(result))}`
            // window.open(url)
            getCurTab.linkUrl = `${baseUrl}?token=${localStorage.getItem('token')}&keyword=${encodeURIComponent(JSON.stringify(result))}`;
            Router.push({
                name: "thirdView",
            });
            setTimeout(() => {
                data.HDICTCustomizedHistoryLoading = true
            }, 1000)
        }
        // ☆处理数据
        const handleAIData = async () => {
            // console.log('data.AIData', data.AIData)
            // console.log(data.isFromHistory, '是否从历史来')
            if (data.isFromHistory) {
                // console.log('data.AIData', data.AIData)
                if (data.AIData) {
                    // 这里要优化一下右侧窗口出现的问题
                    if (data.AIData.aimDescription) {
                        data.historyShowAIData = data.AIData.aimDescription
                        // console.log('data.historyShowAIData', data.historyShowAIData)
                        console.log('data.AIData', data.AIData)
                        // if (data.AIData.aimDescription.sceneList.length !== 0) {
                        //     emit('showProductPackageDetailBox', data.AIData.aimDescription.sceneList)
                        // } else {
                        //     // emit('closeProductPackageDetailBox')
                        // }
                        // 切换右侧抽屉的type
                        if (data.AIData.aimDescription.busDirect == '行业') {
                            if (data.AIData.aimDescription.PPTList.length !== 0) {
                                emit('changeRightModuleType', 'PPT')
                            }
                        } else if (data.AIData.aimDescription.busDirect == '商客') {
                            emit('changeRightModuleType', 'productPackage')
                        } else if (data.AIData.aimDescription.busDirect == 'HDICT') {
                            // 抽屉暂时隐藏，后面要重做
                            // emit('changeRightModuleType', 'HDICT')
                        }
                        // 渲染右侧抽屉
                        if (data.AIData.aimDescription.PPTList.length !== 0) {
                            // PPT右侧抽屉回显
                            emit('openPPTPreview')
                            emit('clearPPTList')
                            emit('addPPT', data.AIData.aimDescription.PPTList)
                        } else if (data.AIData.aimDescription.sceneList.length !== 0) {
                            // 商客右侧抽屉回显
                            emit('showProductPackageDetailBox', data.AIData.aimDescription.sceneList)
                        } else if (data.AIData.aimDescription.HDICTList.length !== 0) {
                            // HDICT右侧抽屉回显
                            // 抽屉暂时隐藏，后面要重做
                            // emit('clearPPTList')
                            // emit("addHDICTPics", data.AIData.aimDescription.HDICTList)
                        }
                    } else {
                        console.log('11')
                        data.writingAIAnswerText = data.AIData.answer
                        emit('closeProductPackageDetailBox')
                        // emit('changeRightModuleType', 'common')
                    }
                } else {
                    console.log('22')
                    emit('closeProductPackageDetailBox')
                    // emit('changeRightModuleType', 'common')
                }
            } else {
                if (data.AIData) {
                    // 打字机打出AI的answer回答
                    data.isAIWriting = true
                    data.AIAnswerText = data.AIData.answer
                    data.goHistoryData.isChat = true
                    // AI判断意图，是定制还是检索（当AI多轮对话结束以后识别出意图才会有data.AIData.busOperate）,防止报错
                    if (data.AIData.busOperate) {
                        data.goHistoryData.isChat = false
                        if (data.AIData.busOperate == '检索') {
                            // AI识别成检索
                            data.getIntentionText = '好的，麒麟AI助手即将为您' + data.AIData.aim
                            data.goHistoryData.busOperate = '检索'
                            data.goHistoryData.aim = data.AIData.aim
                            await typeWriterGetIntention()
                            if (data.AIData.busDirect == 'HDICT') {
                                // 检索的HDICT
                                data.goHistoryData.busDirect = 'HDICT'
                                await getAISearchHDICTData()
                            } else {
                                // 其他的检索
                                await getAISearchData()
                            }
                        } else if (data.AIData.busOperate == '定制') {
                            // AI识别成定制
                            data.goHistoryData.busOperate = '定制'
                            if (data.AIData.busDirect == '行业') {
                                // 定制行业
                                data.goHistoryData.busDirect = '行业'
                                data.getIntentionText = '正在分析用户关键词......'
                                typeWriterGetIntention()
                                // await writingOver();
                            } else if (data.AIData.busDirect == '商客') {
                                // emit('changeRightModuleType', 'productPackage')

                                // 定制商客
                                data.goHistoryData.busDirect = '商客'
                                // console.log('data.AIData', data.AIData)
                                if (data.AIData.flag == 2) {
                                    // 走的推荐定制
                                    await typeWriterAcceptrecommendRecommend()
                                    data.goHistoryData.isRecommend = '是'
                                    // 模拟获取接口走的自由定制
                                    // await typeWriterFreeCustomized()
                                    // data.goHistoryData.isRecommend = '否'
                                    data.AIRecommendSceneList = data.AIData.sceneList
                                    data.goHistoryData.sceneList = data.AIData.sceneList
                                    data.commenTile = data.AIData.keyword.productPackageList[0].key
                                } else {
                                    await typeWriterAIAnswer()
                                    await writingOver();
                                    if (data.AIData.sceneList && data.AIData.sceneList.length !== 0) {
                                        console.log('有推荐的了')
                                        emit('showProductPackageDetailBox', data.AIData.sceneList)
                                    }
                                }
                            } else if (data.AIData.busDirect == 'HDICT') {
                                data.goHistoryData.busDirect = 'HDICT'
                                // 暂时不支持HDICT的定制
                                // storeChatResult({
                                //     sessionId: data.sessionId,
                                //     aimDescription: JSON.stringify(data.goHistoryData)
                                // }).then((res) => {
                                //     console.log('res', res)
                                //     data.chatId = res.data
                                //     data.goHistoryData.chatId = res.data
                                // })
                                // await typeWriterCantCustomizeHDICT()
                                if (data.AIData.keyword.houseType.length + data.AIData.keyword.product.length + data.AIData.keyword.scene.length == 0) {
                                    await HDICTNoData()
                                    data.goHistoryData.HDICTCustomized.isAIKeyword = false
                                } else {
                                    data.goHistoryData.HDICTCustomized.isAIKeyword = true
                                    data.goHistoryData.HDICTCustomized.isJiangSu = data.isJiangSu
                                    // HDICT的抽屉
                                    if (data.isJiangSu) {
                                        if (!data.isSBchange) {
                                            // 抽屉暂时隐藏，后面要重做
                                            // emit('changeRightModuleType', 'HDICT')
                                            // emit('contorlAddingHDICT', 'true')
                                        }
                                    }
                                    await typeWriterHDICTCustomizedText1()
                                }
                            }

                        }
                    } else {
                        // 当没有data.AIData.busOperate的时候说明还在对话阶段，要走typeWriterAIAnswer，把AI说的话打印出来，有了的时候由前端拼接AI需要说的话不走这里
                        emit('chatCloseBox')
                        await typeWriterAIAnswer()
                        await writingOver();
                    }
                }
            }
        }
        // 是否跳转定制页
        const jumpCustomized = (type) => {
            if (type == '1') {
                // console.log('跳转');
                console.log('goCustomizedList', data.goCustomizedList);
                let arr = []
                for (let i of data.goCustomizedList) {
                    if (i.specialTitle == '方案') {
                        arr.push({
                            schemeId: i.id,
                            type: 1
                        })
                    }
                    if (i.specialTitle == '能力') {
                        arr.push({
                            schemeId: i.id,
                            type: 2
                        })
                    }
                    if (i.specialTitle == '场景') {
                        arr.push({
                            schemeId: i.id,
                            type: 3
                        })
                    }
                    if (i.specialTitle == '产品') {
                        arr.push({
                            schemeId: i.id,
                            type: 4
                        })
                    }
                    if (i.specialTitle == '案例') {
                        arr.push({
                            schemeId: i.id,
                            type: 5
                        })
                    }
                }
                // console.log('arr', arr)
                // 把直接传过去调接口的数组arr进行去重
                const seen = new Set();
                const uniqueArr = arr.filter(item => {
                    const identifier = `${item.schemeId}|${item.type}`;
                    return seen.has(identifier) ? false : seen.add(identifier);
                });
                console.log('uniqueArr', uniqueArr)

                data.showGoCustomizedLoading = false
                newGo({
                    list: uniqueArr,
                    source: "2",
                    cover: data.AIData.keyword.solutionList[0].key,
                    conclusion: '谢谢聆听',
                }).then((res) => {
                    // console.log(res);
                    emit('changeResultType', '2')
                    emit('closeSpeakBox')
                    localStorage.setItem('goCustomizedSessionId', data.sessionId)
                    // Router.push({
                    //     path: "/newProject/newProject",
                    //     query: {
                    //         type: 2
                    //     }
                    // });
                    let href = window.location.origin + '/#/newProject/newProject?type=2&comeFrom=aiChat'
                    console.log('href', href)
                    window.open(href, '_blank')
                    data.showGoCustomizedLoading = true
                });
                emit('clearAISession')
            } else {
                // console.log('不跳转');
                data.showJumpCustomizedBtn = false
                writingOver()
                // 再发送一句"我不满意"
                emit('noGood')
            }
            // data.jumpBtnDisabled = true
        }
        // 聊天历史记录里的按钮
        const historyContinue = (type) => {
            if (type == '1') {
                if (data.historyShowAIData.busDirect == '行业') {
                    // 继续定制
                    console.log('定制内容', data.historyShowAIData.goCustomizedList)
                    let arr = []
                    for (let i of data.historyShowAIData.goCustomizedList) {
                        if (i.specialTitle == '方案') {
                            arr.push({
                                schemeId: i.id,
                                type: 1
                            })
                        }
                        if (i.specialTitle == '能力') {
                            arr.push({
                                schemeId: i.id,
                                type: 2
                            })
                        }
                        if (i.specialTitle == '场景') {
                            arr.push({
                                schemeId: i.id,
                                type: 3
                            })
                        }
                        if (i.specialTitle == '产品') {
                            arr.push({
                                schemeId: i.id,
                                type: 4
                            })
                        }
                        if (i.specialTitle == '案例') {
                            arr.push({
                                schemeId: i.id,
                                type: 5
                            })
                        }
                    }
                    // console.log('arr', arr)
                    // 把直接传过去调接口的数组arr进行去重
                    const seen = new Set();
                    const uniqueArr = arr.filter(item => {
                        const identifier = `${item.schemeId}|${item.type}`;
                        return seen.has(identifier) ? false : seen.add(identifier);
                    });
                    // console.log('uniqueArr', uniqueArr)

                    data.historyContinueLoading = true
                    newGo({
                        list: uniqueArr,
                        source: "2",
                        cover: data.historyShowAIData.peopleSaySolutionName,
                        conclusion: '谢谢聆听'
                    }).then((res) => {
                        // console.log(res);
                        emit('changeResultType', '2')
                        emit('closeSpeakBox')
                        localStorage.setItem('goCustomizedSessionId', Route.query.sessionId)
                        localStorage.setItem('isFromHistory', '否')
                        // Router.push({
                        //     path: "/newProject/newProject",
                        //     query: {
                        //         type: 2
                        //     }
                        // });
                        let href = window.location.origin + '/#/newProject/newProject?type=2&comeFrom=aiChat'
                        console.log('href', href)
                        window.open(href, '_blank')
                        data.showGoCustomizedLoading = false
                        data.historyContinueLoading = false
                    });
                    emit('clearAISession')
                } else if (data.historyShowAIData.busDirect == '商客') {
                    console.log(' data.showGoProductLoading', data.showGoProductLoading)
                    data.showGoProductLoading = true
                    console.log('ssssssssssssss', data.goCustomizedProductPackageList)
                    toShopList({
                        productShoppingCarts: data.historyShowAIData.goCustomizedProductBagList,
                        source: "2",
                        title: data.historyShowAIData.commenTile,
                    }).then((res) => {
                        data.showGoProductLoading = false
                        emit('changeResultType', '7')
                        emit('closeSpeakBox')
                        localStorage.setItem('goCustomizedSessionId', Route.query.sessionId)
                        localStorage.setItem('isFromHistory', '否')
                        console.log(' data.showGoProductLoading', data.showGoProductLoading)

                        // Router.push({
                        //     path: "/newProject/newProject",
                        //     query: {
                        //         type: 10
                        //     }
                        // });
                        let href = window.location.origin + '/#/newProject/newProject?type=10'
                        console.log('href', href)
                        window.open(href, '_blank')
                    })
                }
            } else if (type == '2') {
                // 继续定制
                console.log('结束流程')
                data.showHistoryContinueBtn = false
            }
        }
        // 删除某一项的按钮
        const deleteThisOne = (type, specialTitle, id, info) => {
            console.log('data.chatId', data.chatId)
            console.log('type', type)
            console.log('specialTitle', specialTitle)
            console.log('id', id)
            console.log('info', info)
            if (type == 'thinkAbility' || type == 'thinkCase' || type == 'solutionScene' || type == 'solutionCase') {
                console.log('最终去定制的数组', data.goCustomizedList)
                emit('updatePPT', info)
                if (type == 'solutionScene') {
                    // 删除方案自带的场景
                    console.log('data.showSolutionSceneList', data.showSolutionSceneList)
                    data.showSolutionSceneList = data.showSolutionSceneList.filter(item => item.id !== id)
                    data.goHistoryData.abilityList = data.goHistoryData.abilityList.filter(item => item.id !== id)
                    if (data.showSolutionSceneList.length + data.showThinkAbilityList.length + data.showNoSearchAbilityList.length == 0) {
                        data.isAbilityList = false
                        data.writingApplicationCaseText = '二、应用案例'
                    }
                } else if (type == 'thinkAbility') {
                    // 删除要素思考的场景
                    if (data.nowSolutionId) {
                        // 搜到了方案的情况
                        console.log('data.newShowSceneList', data.newShowSceneList)
                        data.newShowSceneList = data.newShowSceneList.filter(item => item.id !== id)
                        data.goHistoryData.abilityList = data.goHistoryData.abilityList.filter(item => item.id !== id)
                        if (data.newShowSceneList.length == 0) {
                            data.isAbilityList = false
                            data.writingApplicationCaseText = '二、应用案例'
                        }
                    } else {
                        // 没有搜到方案的情况
                        console.log('data.showNoSolutionThinkAbilityList', data.showNoSolutionThinkAbilityList)
                        data.showNoSolutionThinkAbilityList = data.showNoSolutionThinkAbilityList.filter(item => item.id !== id)
                        data.goHistoryData.abilityList = data.goHistoryData.abilityList.filter(item => item.id !== id)
                        if (data.showNoSolutionThinkAbilityList.length == 0) {
                            data.isNoNoSolutionAbility = true
                            data.writingNoSolutionCustomized3 = '一、应用案例'
                        }
                    }

                } else if (type == 'thinkCase') {
                    // 删除要素思考的案例
                    if (data.nowSolutionId) {
                        // 搜到了方案的情况
                        console.log('data.showPeopleCasesList', data.showPeopleCasesList)
                        data.showPeopleCasesList = data.showPeopleCasesList.filter(item => item.id !== id)
                        data.goHistoryData.peopleCaseList = data.goHistoryData.peopleCaseList.filter(item => item.id !== id)
                        if (data.showPeopleCasesList.length == 0) {
                            data.isNopeopleCase = true
                        }
                        if (data.showPeopleCasesList.length + data.showSolutionCaseList.length == 0) {
                            data.isShowCasesTitle = false
                        }
                    } else {
                        // 没有搜到方案的情况
                        console.log('data.showNoSolutionPeopleCasesList', data.showNoSolutionPeopleCasesList)
                        data.showNoSolutionPeopleCasesList = data.showNoSolutionPeopleCasesList.filter(item => item.id !== id)
                        data.goHistoryData.peopleCaseList = data.goHistoryData.peopleCaseList.filter(item => item.id !== id)
                        if (data.showNoSolutionPeopleCasesList.length == 0) {
                            data.isNoNosolutionCases = true
                        }
                    }
                } else if (type == 'solutionCase') {
                    data.showSolutionCaseList = data.showSolutionCaseList.filter(item => item.id !== id)
                    data.goHistoryData.solutionCaseList = data.goHistoryData.solutionCaseList.filter(item => item.id !== id)
                    if (data.showSolutionCaseList.length == 0) {
                        data.isShowCases = false
                    }
                    if (data.showPeopleCasesList.length + data.showSolutionCaseList.length == 0) {
                        data.isShowCasesTitle = false
                    }
                }
                // 删除最终去定制的数组中匹配的项
                data.goCustomizedList = data.goCustomizedList.filter(item => {
                    return !(item.specialTitle === specialTitle && item.id === id);
                });
                data.goHistoryData.goCustomizedList = data.goCustomizedList
                // 删除存入历史记录里PPTList中匹配的项
                const startIndex = data.goHistoryData.PPTList.findIndex(item => item.smallTitle === info.title);
                if (startIndex !== -1) {
                    data.goHistoryData.PPTList.splice(startIndex, info.slideUrls.length + 1);
                }
                console.log('xxxxxxxxxx', data.goHistoryData.abilityList)
            } else if (type == 'productBag') {
                // console.log('data.goCustomizedProductPackageList', data.goCustomizedProductPackageList);
                // console.log('data.goHistoryData.SupplementaryProductList', data.goHistoryData.SupplementaryProductList)
                // 只删除type为2且id匹配的项
                data.goCustomizedProductPackageList = data.goCustomizedProductPackageList.filter(item => {
                    if (item.type === 2) {
                        return item.productId !== id;
                    }
                    return true;
                });
                data.showPeopleSayProductList = data.showPeopleSayProductList.filter(item => item.id !== id)
                data.goHistoryData.SupplementaryProductList = data.goHistoryData.SupplementaryProductList.filter(item => item.name !== info.displayText)
                data.goHistoryData.goCustomizedProductBagList = data.goCustomizedProductPackageList
                if (data.showPeopleSayProductList.length == 0) {
                    data.isNoPeopleSayProduct = true
                }
                // console.log('data.goCustomizedProductPackageList', data.goCustomizedProductPackageList);
            }

            updateChatResult({
                sessionId: data.sessionId,
                id: data.chatId,
                aimDescription: JSON.stringify(data.goHistoryData)
            }).then((res) => {
                console.log('res', res)
            })
        }
        // 历史记录里删除某一项的按钮
        const deleteHistoryThisOne = (info, type) => {
            console.log('info', info)
            console.log('data.historyShowAIData.chatId,', data.historyShowAIData.chatId,)
            if (type == 'thinkAbility' || type == 'thinkCase' || type == 'solutionCases') {
                //
                emit('updatePPT', info)
                console.log('data.showThinkAbilityList', data.showThinkAbilityList)
                if (type == 'thinkAbility') {
                    data.historyShowAIData.abilityList = data.historyShowAIData.abilityList.filter(item => item.id !== info.id)
                } else if (type == 'thinkCase') {
                    data.historyShowAIData.peopleCaseList = data.historyShowAIData.peopleCaseList.filter(item => item.id !== info.id)
                } else if (type == 'solutionCases') {
                    data.historyShowAIData.solutionCaseList = data.historyShowAIData.solutionCaseList.filter(item => item.id !== info.id)
                }

                data.historyShowAIData.goCustomizedList = data.historyShowAIData.goCustomizedList.filter(item => {
                    return !(item.specialTitle === info.specialTitle && item.id === info.id);
                });
                // 删除存入历史记录里PPTList中匹配的项
                const startIndex = data.historyShowAIData.PPTList.findIndex(item => item.smallTitle === info.title);
                if (startIndex !== -1) {
                    data.historyShowAIData.PPTList.splice(startIndex, info.slideUrls.length + 1);
                }
            } else if (type == 'productBag') {
                // 产品包的历史记录
                // console.log('historyShowAIData.SupplementaryProductList', data.historyShowAIData.SupplementaryProductList)
                console.log('data.historyShowAIData.goCustomizedProductBagList', data.historyShowAIData.goCustomizedProductBagList)
                // 删除页面上显示的对应的内容
                data.historyShowAIData.SupplementaryProductList = data.historyShowAIData.SupplementaryProductList.filter(item => item.name !== info.name)
                // 删除历史记录里去定制的数组里匹配的项，type=2是补充产品
                data.historyShowAIData.goCustomizedProductBagList = data.historyShowAIData.goCustomizedProductBagList.filter(item => {
                    if (item.type === 2) {
                        return item.productId !== info.id;
                    }
                    return true;
                });
            }

            updateChatResult({
                sessionId: data.sessionId,
                id: data.historyShowAIData.chatId,
                aimDescription: JSON.stringify(data.historyShowAIData)
            }).then((res) => {
                console.log('res', res)
            })
        }
        // 删除"来源"
        const deleteWhereFrom = (str) => {
            // 找到最后一个"。"的索引
            const lastIndex = str.lastIndexOf("。");

            // 如果存在"。"，则保留从开头到最后一个"。"的位置（包含"。"）
            if (lastIndex !== -1) {
                str = str.substring(0, lastIndex + 1);  // +1 是为了包含"。"本身
            }
            return str
        }
        onMounted(() => {
            typingWorker = new Worker(new URL('@/workers/typingWorker.js', import.meta.url), { type: 'module' });
            typingWorker.onmessage = (e) => {
                if (e.data.type === 'typingUpdate') {
                    if (e.data.id && typeof data[e.data.id] !== 'undefined') {
                        data[e.data.id] = e.data.text;
                    }
                    // 处理 item 映射表的更新
                    if (e.data.itemId && itemMap.has(e.data.itemId)) {
                        const { item, prop } = itemMap.get(e.data.itemId);
                        item[prop] = e.data.text;
                    }
                }
                if (e.data.type === 'typingDone') {
                    if (pendingResolvers[e.data.id]) {
                        pendingResolvers[e.data.id]();
                        delete pendingResolvers[e.data.id];
                    }
                    // 处理 item 映射表的完成
                    if (e.data.itemId && itemMap.has(e.data.itemId)) {
                        const { resolve } = itemMap.get(e.data.itemId);
                        resolve();
                        itemMap.delete(e.data.itemId);
                    }
                }
            };
            // console.log('thinkingStatus', data.thinkingStatus)
            // console.log('localStorage.getItem("userInfo").orgNamePath', JSON.parse(localStorage.getItem("userInfo")).orgNamePath)
            const orgNamePath = JSON.parse(localStorage.getItem("userInfo")).orgNamePath
            // data.isJiangSu = orgNamePath.includes("江苏公司")
            // console.log('是否包含江苏公司:', data.isJiangSu)
            data.isFromHistory = localStorage.getItem("isFromHistory") == '是' ? true : false
            handleAIData()

        })
        onUnmounted(() => {
            if (typingWorker) {
                typingWorker.terminate();
                itemMap.clear(); // 清理映射表
            }
        });
        // 保证每次定制删除到最终必须保证有一个有id的东西可以去定制
        const showLoadingDeleteBtn = (type) => {
            if (data.nowSolutionId) {
                let arr1 = []
                let arr2 = []
                let arr3 = []
                for (let i of data.showSolutionCaseList) {
                    if (i.id) {
                        arr1.push(i)
                    }
                }
                for (let i of data.showPeopleCasesList) {
                    if (i.id) {
                        arr2.push(i)
                    }
                }
                for (let i of data.newShowSceneList) {
                    if (i.id) {
                        arr3.push(i)
                    }
                }
                if (arr1.length + arr2.length + arr3.length > 1) {
                    return true
                }
                return false
            } else {
                if (data.goCustomizedList.length > 1) {
                    return true
                }
                return false
            }

        }
        const showHistoryLoadingDeleteBtn = () => {
            let arr1 = []
            let arr2 = []
            let arr3 = []
            for (let i of data.historyShowAIData.solutionCaseList) {
                if (i.id) {
                    arr1.push(i)
                }
            }
            for (let i of data.historyShowAIData.peopleCaseList) {
                if (i.id) {
                    arr2.push(i)
                }
            }
            for (let i of data.historyShowAIData.abilityList) {
                if (i.id) {
                    arr3.push(i)
                }
            }
            if (arr1.length + arr2.length + arr3.length > 1) {
                return true
            }
            return false
        }
        // 历史记录里的检索跳转按钮
        const historyShowSearchBtn = (list) => {
            let allLength = 0
            for (let i of list.solutionList) {
                allLength += i.solution.length
            }
            for (let i of list.abilityList) {
                allLength += i.ability.length
            }
            for (let i of list.sceneList) {
                allLength += i.scene.length
            }
            for (let i of list.productList) {
                allLength += i.product.length
            }
            for (let i of list.productPackageList) {
                allLength += i.productBag.length
            }
            if (allLength == 0) {
                return false
            } else {
                return true
            }
        }
        return {
            ...toRefs(data),
            handleAIData,
            jumpCustomized,
            noSolutionContinue,
            isGoProductPackage,
            historyContinue,
            numberToChinese,
            jumpToHDICT,
            jumpToSearch,
            goHDICTCustomizedBtn,
            HDICTCustomizedJump,
            deleteThisOne,
            deleteHistoryThisOne,
            getPersonChooseSolutionData,
            newWritingNoSolutionWords,
            newWritingNoSolution,
            confirmOutlineText,
            historyStreamChatContinue,
            deleteWhereFrom,
            showSolutionDetail,
            showLoadingDeleteBtn,
            showHistoryLoadingDeleteBtn,
            historyShowSearchBtn,
        };
    },
});
</script>

<style lang="scss" scoped>
.message-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

/* 用户消息的样式，头像在右边 */
.user-message {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

// /* AI消息的样式，头像在左边 */
.ai-message {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
}

.user-box {
    .message {
        text-align: left;
        line-height: 36px;
        max-width: 600px;
        background-color: #2D65F7;
        box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
        padding: 10px 20px 10px 20px;
        border-radius: 15px 0px 15px 15px;
        color: #fff;
    }
}

.ai-box {
    .message {
        max-width: 920px;
        background-color: #fff;
        box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
        padding: 10px 20px 10px 20px;
        border-radius: 0 15px 15px 15px;

        .words {
            // line-height: 36px;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

.loading-spinner2 {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.sureBtn {
    width: 64px;
    height: 34px;
    background: #0C70EB;
    border-radius: 4px 4px 4px 4px;
    color: #FFFFFF;
    font-size: 14px;
}

.historyDownloadBtn {
    width: 80px;
    height: 34px;
    background: #0C70EB;
    border-radius: 4px 4px 4px 4px;
    color: #FFFFFF;
    font-size: 14px;
}

.cancelBtn {
    width: 64px;
    height: 34px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #D1D0D8;
    color: #A2ABB5;
    font-size: 14px;
}

.searchedWord {
    color: #16CA37;
    font-weight: bold;
}

.unsearchedWord {
    color: #FFA11D;
    font-weight: bold;
}


.textIndent30 {
    text-indent: 30px;
}

.textIndent60 {
    text-indent: 60px;
}

.item-container {
    position: relative;
    transition: all 0.3s ease;
}

.item-container:hover .delete-btn {
    width: 50px;
    height: 25px;
    margin-left: 10px;
    opacity: 1;
    transform: translateX(0);
}

.item-container:hover .redPoint {
    display: none;
}

.delete-btn {
    width: 0;
    height: 0;
    background-color: rgb(218, 18, 18);
    color: #fff;
    border-radius: 5px;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    overflow: hidden;
    white-space: nowrap;
    margin: auto 0;
}

.outlineTextStyle {
    border: 2px solid #eee;
    border-radius: 20px;

    .line {
        width: 100%;
        height: 60px;
        border-bottom: 2px solid #eee;
        font-size: 24px;
        line-height: 60px;
        text-indent: 20px;
    }

    // 转JSON以后的大标题
    .overviewOutlineTitle {
        font-size: 24px;
        font-weight: bold;
    }



    // 每个大类下小类的标题，level为3的
    .overviewOutlineSubTitle {
        font-size: 18px;
    }

    // 每个小类下的具体内容，level为4的
    .overviewOutlineContent {
        font-size: 16px;
    }

    ::v-deep(.ant-input) {
        width: 700px !important
    }
}

// 政策背景、需求分析、方案概述、应用场景 四大类
.overviewOutlineCategoryTitle {
    font-size: 24px;
    font-weight: bold;
}

.zhengceContent {
    transition: max-height 1s ease;
    overflow: hidden;
    max-height: 3000px;
    /* 根据你的内容调整这个值 */
}

.zhengceHeight0 {
    max-height: 0 !important;
}

.thinkingContent {
    transition: max-height 1s ease;
    overflow: hidden;
    max-height: 3000px;

    /* 根据你的内容调整这个值 */
    .thinkingTextStyle {
        background-color: #f0f0f0;
        border-radius: 10px;
        padding: 10px;
        max-height: 300px;
        overflow-y: auto;
    }
}

.thinkingHeight0 {
    max-height: 0 !important;
}

.guideWordsContent {
    transition: max-height 1s ease;
    overflow: hidden;
    max-height: 3000px;

    /* 根据你的内容调整这个值 */
    .eachSolution {
        transition: all 1s ease;
        border: 2px solid transparent;

        &:hover {
            border: 2px solid #0C70EB;
        }
    }
}

.guideWordsHeight0 {
    max-height: 0 !important;
}

.boxShadow {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    transition: background-color 0.5s ease;
}

.blackBoxShadow {
    background-color: rgba(0, 0, 0, 0.8);
}

.selected-item {
    border: 1px solid #000;
    transition: border-color 0.5s ease;
}

.eachSolutionStyle {
    border: 1px solid transparent;
    transition: border-color 0.5s ease;
}

.eachSolutionStyle:hover {
    border-color: #000;
}

.no-satisfaction-btn {
    border: 1px solid transparent;
    transition: border-color 0.5s ease;
}

.no-satisfaction-btn:hover {
    border-color: #fff !important;
}

@keyframes pulseScale {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.pulseScale {
    animation: pulseScale 2s infinite;
}

.redPoint {
    margin-left: 4px;
    width: 6px;
    height: 6px;
    background-color: red;
    border-radius: 4px;
}
</style>
