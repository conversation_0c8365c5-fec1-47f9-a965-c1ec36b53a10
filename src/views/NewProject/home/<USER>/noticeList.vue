<template>
  <div class="informContent" v-if="scrollShow">
    <div
      class="scroll-wrapper"
      @mouseenter="pauseScroll"
      @mouseleave="startScroll"
      ref="scrollWrapper"
    >
      <div
        class="scroll-content"
        ref="scrollContent"
        :style="{ transform: `translateX(${offset}px)` }"
      >
        <div
          v-for="(item, index) in loopList"
          :key="index"
          class="noticeContent"
          @click="goDetail(item.id)"
        >
          <img
            width="60"
            height="24"
            src="@/assets/images/home/<USER>"
          />
          <div class="details margin_l_8">
            <div class="word">{{ item.title }}：</div>
            <div class="text htmlContentImg peaceTable">
              {{ stripHtml(item.content) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="topClose" @mouseenter="closeEnter" @mouseleave="closeLeave">
    <img
      class="pointer"
      v-if="closeShow"
      @click="closeScroll"
      width="14"
      height="14"
      src="@/assets/images/home/<USER>"
    />
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onMounted,
  onBeforeUnmount,
  nextTick,
} from "vue";
import { getMsgList } from "@/api/community/index.js";
import { useRouter } from "vue-router";

export default defineComponent({
  setup() {
    const data = reactive({
      noticeList: [],
      loopList: [],
      scrollShow: true,
      isPaused: false,
      closeShow: false,
      scrollSpeed: 80, //数值越大滚动越快
      offset: 0,
      contentWidth: 0,
      singleLoopWidth: 0,
      animationFrame: null,
      lastTimestamp: null,
      searchData: {
        pageSize: 10,
        pageNo: 1,
        type: "2",
      },
    });

    const scrollWrapper = ref(null);
    const scrollContent = ref(null);
    const Router = useRouter();

    const getData = async () => {
      const res = await getMsgList(data.searchData);
      data.noticeList = res.data.rows.slice(0, 3);
      data.loopList = [
        ...data.noticeList,
        ...data.noticeList,
        ...data.noticeList,
      ];
      await nextTick();
      calculateDimensions();
      startScroll();
    };

    const calculateDimensions = () => {
      if (scrollContent.value && data.noticeList.length > 0) {
        const firstItem = scrollContent.value.children[0];
        if (firstItem) {
          const itemRect = firstItem.getBoundingClientRect();
          const itemWidth = Math.round(itemRect.width) + 120;
          data.singleLoopWidth = Math.round(itemWidth * data.noticeList.length);
          data.contentWidth = Math.round(itemWidth * data.loopList.length);
          scrollContent.value.style.width = `${data.contentWidth}px`;
        }
      }
    };

    const animateScroll = (timestamp) => {
      if (!data.lastTimestamp) {
        data.lastTimestamp = timestamp;
        data.animationFrame = requestAnimationFrame(animateScroll);
        return;
      }
      const deltaTime = timestamp - data.lastTimestamp;
      data.lastTimestamp = timestamp;
      if (!data.isPaused && data.singleLoopWidth > 0) {
        data.offset -= (data.scrollSpeed * deltaTime) / 1000;
        if (Math.abs(data.offset) >= data.singleLoopWidth) {
          data.offset += data.singleLoopWidth;
        }
      }
      data.animationFrame = requestAnimationFrame(animateScroll);
    };

    const startScroll = () => {
      data.isPaused = false;
      if (!data.animationFrame) {
        data.lastTimestamp = null;
        data.animationFrame = requestAnimationFrame(animateScroll);
      }
    };

    const pauseScroll = () => {
      data.isPaused = true;
    };

    const stopScroll = () => {
      if (data.animationFrame) {
        cancelAnimationFrame(data.animationFrame);
        data.animationFrame = null;
      }
    };

    const closeScroll = () => {
      data.closeShow = false;
      data.scrollShow = false;
      stopScroll();
    };

    const goDetail = (id) => {
      Router.push({
        name: "communityDetail",
        query: { id },
      });
    };

    onMounted(() => {
      getData();
    });

    onBeforeUnmount(() => {
      stopScroll();
    });

    const closeEnter = () => {
      if (data.scrollShow) data.closeShow = true;
    };

    const closeLeave = () => {
      data.closeShow = false;
    };

    const stripHtml = (html) => {
      return html.replace(/<[^>]+>/g, "");
    };

    return {
      ...toRefs(data),
      scrollWrapper,
      scrollContent,
      pauseScroll,
      closeEnter,
      stripHtml,
      closeLeave,
      startScroll,
      closeScroll,
      goDetail,
    };
  },
});
</script>

<style lang="scss" scoped>
.details {
  display: flex;
  align-items: center;
  width: 470px;
}

.word {
  font-weight: 500;
  font-size: 14px;
  color: #00060e;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 310px;
}

.text {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 6, 14, 0.6);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  word-break: break-all;
  flex: 1;
}
.informContent {
  position: absolute;
  top: 0;
  max-width: 95%;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  z-index: 10;
}

.topClose {
  width: 14px;
  height: 14px;
  position: absolute;
  top: 7px;
  right: 10px;
  z-index: 10;
}

.scroll-wrapper {
  overflow: hidden;
  width: 100%;
  cursor: pointer;
}

.scroll-content {
  display: flex;
  gap: 120px;
  will-change: transform;
  backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
}

.noticeContent {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(183, 214, 253, 0.4);
  border-radius: 20px;
  max-width: 578px;
  flex-shrink: 0;
  transform: translateZ(0);
  user-select: none;
}

@media (max-width: 768px) {
  .informContent {
    top: 60px;
    max-width: 90%;
  }

  .noticeContent {
    max-width: 300px;
    padding: 6px 10px;
  }

  .details {
    width: 220px;
  }

  .word {
    max-width: 150px;
  }
}
</style>

<style lang="scss">
.htmlContentImg img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}
</style>