<template>
  <div class="contents">
    <div class="dispatch-center">
      <div class="top-summary">
        <div class="card-row">
          <div class="card pending">
            <div class="content">
              <div class="number">
                {{ todoCount }}<span class="unit">个</span>
              </div>
              <div class="title">待办工单</div>
            </div>
          </div>
          <div class="card processed">
            <div class="content">
              <div class="number">
                {{ finishedCount }}<span class="unit">个</span>
              </div>
              <div class="title">已办工单</div>
            </div>
          </div>
          <div class="card scheduled">
            <div class="content">
              <div class="number">
                {{ scheduleCount }}<span class="unit">个</span>
              </div>
              <div class="title">调度工单</div>
            </div>
          </div>
        </div>

        <div v-if="!isEcologicalPartner" class="apply-dispatch-row">
          <div class="apply-card" @click="handleDispatchClick('售前调度')">
            <img :src="applyIcons.pre" />
          </div>
          <div class="apply-card" @click="handleDispatchClick('售中调度')">
            <img :src="applyIcons.mid" />
          </div>
          <div class="apply-card" @click="handleDispatchClick('售后调度')">
            <img :src="applyIcons.after" />
          </div>
        </div>
      </div>

      <div class="sale-tabs">
        <PreSale />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, watch, provide } from "vue";
import PreSale from "./components/PreSale.vue";
import { getStatisticsData } from "@/api/dispatchCenter/backlog&completed.js";
import { useRouter } from "vue-router";
defineOptions({
  name: "DispatchCenter",
});

const userInfo = ref(null);
const Router = useRouter()
const isEcologicalPartner = computed(() => {
  return userInfo.value?.roleKeyList?.includes("ecologicalPartner");
});
/*

  统计数据处理

*/

const statistics = ref({
  preTodoCount: 0, // 售前待办
  preFinishedCount: 0, // 售前已办
  midTodoCount: 0, // 售中待办
  midFinishedCount: 0, // 售中已办
  afterTodoCount: 0, // 售后待办
  afterFinishedCount: 0, // 售后已办
});

const fetchData = async () => {
  try {
    const res = await getStatisticsData();
    if (res.code === 200 && res.data) {
      statistics.value = res.data;
    } else {
      console.error("获取数据失败:", res.msg);
    }
  } catch (error) {
    console.error("接口请求异常:", error);
  }
};

const todoCountReal = computed(() => {
  return (
    statistics.value.preTodoCount +
    statistics.value.midTodoCount +
    statistics.value.afterTodoCount
  );
});
const finishedCountReal = computed(() => {
  return (
    statistics.value.preFinishedCount +
    statistics.value.midFinishedCount +
    statistics.value.afterFinishedCount
  );
});
const scheduleCountReal = computed(() => {
  return todoCountReal.value + finishedCountReal.value;
});

const todoCount = ref(0);
const finishedCount = ref(0);
const scheduleCount = ref(0);
function animateCount(targetRef, endValue, duration = 500) {
  const startValue = targetRef.value;
  const range = endValue - startValue;
  if (range === 0) return;
  const startTime = performance.now();

  function step(currentTime) {
    const elapsed = currentTime - startTime;
    if (elapsed < duration) {
      const progress = elapsed / duration;
      targetRef.value = Math.floor(startValue + range * progress);
      requestAnimationFrame(step);
    } else {
      targetRef.value = endValue;
    }
  }
  requestAnimationFrame(step);
}

const applyIcons = {
  pre: new URL("@/assets/images/Coordination/preDispatch.png", import.meta.url)
    .href,
  mid: new URL("@/assets/images/Coordination/midDispatch.png", import.meta.url)
    .href,
  after: new URL(
    "@/assets/images/Coordination/afterDispatch.png",
    import.meta.url
  ).href
};
// 调度配置
const dispatchConfig = {
  售前调度: {
    to: "starWork",
    checkPermission: () => true,
  },
  售中调度: {
    to: "coordination",
    checkPermission: (userInfo) =>
      userInfo.roleKeyList.includes("deliveryManager"),
    errorMessage: "目前售中调度工单仅支持交付经理发起",
  },
  售后调度: {
    to: "",
    checkPermission: () => false,
    errorMessage: "售后调度工单流程暂未开放，敬请期待！",
  },
};
const handleDispatchClick = (type) => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo")) || {};
  const config = dispatchConfig[type];
  if (!config) return;
  if (!config.checkPermission(userInfo)) {
    message.warning(config.errorMessage);
    return;
  }
  const params = {
    action: "edit",
    from: "center",
    to: config.to,
    active: "调度中心",
  };
  const searchParams = new URLSearchParams(params);
  window.location.replace(
    window.location.origin +
      "/#/dispatchCenter/transfer?" +
      searchParams.toString()
  );
};
watch(
  () => todoCountReal.value,
  (newVal) => {
    animateCount(todoCount, newVal);
  }
);
watch(
  () => finishedCountReal.value,
  (newVal) => {
    animateCount(finishedCount, newVal);
  }
);
watch(
  () => scheduleCountReal.value,
  (newVal) => {
    animateCount(scheduleCount, newVal);
  }
);
provide("refreshDashboard", fetchData);
onMounted(() => {
  const infoStr = localStorage.getItem("userInfo");
  try {
    userInfo.value = JSON.parse(infoStr) || {};
  } catch {
    userInfo.value = {};
  }
  fetchData();
});
</script>
<style lang="scss" scoped>
.contents {
  background-image: url("@/assets/images/dispatchCenter/dispatchCenterBg.png");
  background-attachment: fixed;
  background-size: cover;
  min-height: 100vh;
}
.dispatch-center {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
  .top-summary {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    .card-row {
      display: contents;

      .card {
        grid-column: span 2;
        position: relative;
        border-radius: 6px;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          inset: 0;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center;
          z-index: 0;
        }

        > .content {
          position: relative;
          z-index: 1;
          padding: 25px;
        }
        .number {
          font-size: 40px;
          font-weight: bold;
          color: #2e7fff;
          text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
          display: inline-flex;
          align-items: baseline;
          gap: 4px;
        }
        .number .unit {
          font-weight: 400;
          font-size: 18px;
          color: rgba(35, 61, 91, 0.65);
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .title {
          font-weight: bold;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      // 待办卡片
      .pending::before {
        background-image: url("@/assets/images/dispatchCenter/pendingBg.png");
      }

      // 已办卡片
      .processed::before {
        background-image: url("@/assets/images/dispatchCenter/completedBg.png");
      }

      // 调度卡片
      .scheduled::before {
        background-image: url("@/assets/images/dispatchCenter/dispatchBg.png");
      }
    }
    .apply-dispatch-row {
      grid-column: 1 / -1;
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      column-gap: 20px;

      .apply-card {
        grid-column: span 2;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0;

        img {
          width: 100%;
          height: auto;
        }
      }
    }
  }

  .sale-tabs {
    margin-top: 20px;
  }
}
.schemeSupport {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .text {
    font-size: 22px;
    color: rgba(0,0,0,0.75);
  }
}
</style>

<style lang="scss">
.modalTitle .ant-modal-header {
  background-image: url("@/assets/images/Coordination/modelBac.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>


