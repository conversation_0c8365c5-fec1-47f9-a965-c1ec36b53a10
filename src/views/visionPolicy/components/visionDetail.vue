<template>
  <div class="loading-overlay" v-if="viewLoading">
    <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
  </div>
  <div class="top_nav">
    <div class="left_nav">
      <span class="title" @click="back">{{ Route.query.type == "prefecture" ? Route.query.zoneName : "政策洞察" }}</span>
      <span class="title"> / </span>
      <span class="current">
        {{ detailData.name }}
      </span>
    </div>
    <div class="right_nav">
      <div @click="backLast" style="margin-right: 20px">返回</div>
    </div>
  </div>

  <div class="contentZone">
    <div class="text">
      <div>{{ detailData.name || "-" }}</div>
      <div class="ticket">
        <!--<a-button
          type="primary"
          v-if="policyShow"
          @click="addTicket"
          style="
            background: #0c70eb;
            border-radius: 4px;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
          "
        >
          发起工单
        </a-button>-->
      </div>
    </div>
    <div style="margin: 0 80px">
      <div class="classify">
        <div class="flex">
          <div style="padding: 14px 0">
            <span class="title">行业分类：</span>
            <span class="word">{{ detailData.industryName }}</span>
          </div>
        </div>
        <div class="flex align-center">
          <div class="time margin_r_12">发布日期：{{ createTime }}</div>
          <div class="time">访问量：{{ detailData.viewCount }}</div>
        </div>
      </div>
      <div class="description htmlContentImg" v-html="detailData.details"></div>
    </div>

    <div class="fileInfo">
      <div
        v-for="(item, index) in detailData.fileList"
        :key="index"
        style="margin-bottom: 10px"
      >
        <img
          src="@/assets/images/solution/detail/text.png"
          alt=""
          style="width: 40px; height: 40px"
        />
        <a @click="previewFile(item)">{{ item.name }}</a>
      </div>
    </div>
  </div>

  <div class="belowContent" v-if="tableList && tableList.length > 0">
    <div class="textProject">相关基础方案</div>
    <div class="cardContent">
      <div class="card_total flex-1">
        <template v-for="(item, index) in paginatedData" :key="index">
          <div
            :class="[
              'card_content',
              {
                cardActive: cardActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && tableList.length < 3,
                bottomLine:
                  (index == tableList.length - 1 ||
                    index == tableList.length - 2) &&
                  index > 1,
              },
            ]"
            @mouseenter="contentColor(index)"
            @mouseleave="contentLeave"
            @click="proDetail(item)"
          >
            <div style="display: flex; margin: 24px">
              <div>
                <div
                  style="
                    width: 168px;
                    height: 105px;
                    text-align: center;
                    position: relative;
                  "
                  :style="backgroundStyles()"
                >
                  <p
                    style="
                      font-weight: 700;
                      display: block;
                      color: #1f82c8;
                      position: absolute;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      font-size: 10px;
                    "
                  >
                    {{ item.name }}
                  </p>
                </div>
              </div>
              <div class="card_center">
                <div class="card_text">
                  <div class="card_tag">
                    <div class="card_title">{{ item.name }}</div>
                  </div>
                  <div class="cityStyle" v-if="item.provider">
                    {{ item.provider }}
                  </div>
                </div>
                <div class="flex" style="justify-content: space-between">
                  <div class="flex">
                    <a-tag
                      color="#D7E6FF"
                      v-if="item.labelName && item.labelName[0]"
                      style="
                        display: block;
                        color: rgba(0, 0, 0, 0.45);
                        background-color: transparent;
                        border: 1px solid #d9d9d9;
                        line-height: 17px;
                      "
                      >{{ item.labelName[0] }}</a-tag
                    >
                    <a-tag
                      color="#D7E6FF"
                      v-if="item.labelName && item.labelName[1]"
                      style="
                        display: block;
                        color: rgba(0, 0, 0, 0.45);
                        background-color: transparent;
                        border: 1px solid #d9d9d9;
                        line-height: 17px;
                      "
                      >{{ item.labelName[1] }}</a-tag
                    >
                  </div>
                </div>
                <div class="card_des">
                  {{ item.description }}
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      margin-right: 5px;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        margin-right: 18px;
                      "
                    >
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.viewCount"
                        >{{ item.viewCount }}</span
                      >
                      <span v-else>-</span>
                    </div>
                    <div style="display: flex; align-items: center">
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.downloadCount"
                        >{{ item.downloadCount }}</span
                      >
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="layPage">
        <a-pagination
          v-model:pageSize="pageItemSize"
          v-model:current="currentPage"
          :pageSizeOptions="pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="tableList.length"
          class="mypage"
        />
      </div>
    </div>
  </div>

  <div class="belowContent" v-if="sceneList && sceneList.length > 0">
    <div class="textProject">相关场景方案</div>
    <div class="cardContent">
      <div class="card_total flex-1">
        <template v-for="(item, index) in paginatedSceneData" :key="index">
          <div
            :class="[
              'card_content',
              {
                cardActive: cardSceneActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && sceneList.length < 3,
                bottomLine:
                  (index == sceneList.length - 1 ||
                    index == sceneList.length - 2) &&
                  index > 1,
              },
            ]"
            @mouseenter="contentSceneColor(index)"
            @mouseleave="contentSceneLeave"
            @click="proSceneDetail(item)"
          >
            <div style="display: flex; margin: 24px">
              <div>
                <div
                  style="
                    width: 168px;
                    height: 105px;
                    text-align: center;
                    position: relative;
                  "
                  :style="backgroundStyles()"
                >
                  <p
                    style="
                      font-weight: 700;
                      display: block;
                      color: #1f82c8;
                      position: absolute;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      font-size: 10px;
                    "
                  >
                    {{ item.name }}
                  </p>
                </div>
              </div>
              <div class="card_center">
                <div class="card_text">
                  <div class="card_tag">
                    <div class="card_title">{{ item.name }}</div>
                  </div>
                  <div class="cityStyle" v-if="item.provider">
                    {{ item.provider }}
                  </div>
                </div>
                <div class="flex" style="justify-content: space-between">
                  <div class="flex">
                    <a-tag
                      color="#D7E6FF"
                      v-if="item.labelName && item.labelName[0]"
                      style="
                        display: block;
                        color: rgba(0, 0, 0, 0.45);
                        background-color: transparent;
                        border: 1px solid #d9d9d9;
                        line-height: 17px;
                      "
                      >{{ item.labelName[0] }}</a-tag
                    >
                    <a-tag
                      color="#D7E6FF"
                      v-if="item.labelName && item.labelName[1]"
                      style="
                        display: block;
                        color: rgba(0, 0, 0, 0.45);
                        background-color: transparent;
                        border: 1px solid #d9d9d9;
                        line-height: 17px;
                      "
                      >{{ item.labelName[1] }}</a-tag
                    >
                  </div>
                </div>
                <div class="card_des">
                  {{ item.summary }}
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      margin-right: 5px;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        margin-right: 18px;
                      "
                    >
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.viewCount"
                        >{{ item.viewCount }}</span
                      >
                      <span v-else>-</span>
                    </div>
                    <div style="display: flex; align-items: center">
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.downloadCount"
                        >{{ item.downloadCount }}</span
                      >
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="layPage">
        <a-pagination
          v-model:pageSize="pageSceneSize"
          v-model:current="currentScenePage"
          :pageSizeOptions="pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="sceneList.length"
          class="mypage"
        />
      </div>
    </div>
    <div v-if="emptySceneShow" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
  </div>

  <!-- <a-modal
    :visible="previewVisible"
    title="新增工单"
    @cancel="closeModal"
    :footer="null"
    width="1000px"
    class="ticketView"
  >
    <ticket
      v-if="ticketShow"
      @ticketDetail="ticketDetail"
      @cancelTicket="cancelTicket"
    />
  </a-modal> -->
</template>

<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { policyDetail } from "@/api/visionPolicy/visionList";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import bac from "@/assets/images/noDataBac.png";
import ticket from "./ticket.vue";
import eventBus from "@/utils/eventBus";

export default defineComponent({
  components: { ticket },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const roleKeyList = JSON.parse(
      localStorage.getItem("userInfo")
    ).roleKeyList;
    const data = reactive({
      detailData: {},
      createTime: "",
      viewLoading: false,
      ticketShow: false,
      policyShow: roleKeyList.includes("cityIndustryManager"),
      tableList: [],
      sceneList: [],
      pageSizeOptions: ["6", "20", "30", "50"],
      backgroundImage: bac,
      cardActive: "-1",
      cardSceneActive: "-1",
      currentPage: 1,
      pageItemSize: 6,
      currentScenePage: 1,
      pageSceneSize: 6,
      emptyShow: false,
      emptySceneShow: false,
      previewVisible: false,
    });

    const paginatedData = computed(() => {
      const start = (data.currentPage - 1) * data.pageItemSize;
      const end = start + data.pageItemSize;
      return data.tableList.slice(start, end);
    });

    const paginatedSceneData = computed(() => {
      const start = (data.currentScenePage - 1) * data.pageSceneSize;
      const end = start + data.pageSceneSize;
      return data.sceneList.slice(start, end);
    });

    const getDetail = async () => {
      const res = await policyDetail(Route.query.id);
      if (res.code === 200) {
        data.detailData = res.data;
        data.tableList = res.data.solutionList;
        data.sceneList = res.data.sceneList;
        if (data.tableList.length === 0) {
          data.emptyShow = true;
        }
        if (data.sceneList.length === 0) {
          data.emptySceneShow = true;
        }
        // data.tableList.forEach((item) => {
        //   item.labelName = item.labelName.split(",");
        //   item.provider = item.provider.split("/")[1];
        // });
        if (res.data.publishTime) {
          data.createTime = res.data.publishTime.slice(0, 10);
        } else {
          data.createTime = res.data.createTime.slice(0, 10);
        }
      }
    };
    getDetail();

    const back = () => {
      if (Route.query.type == "prefecture") {
        Router.push({
          name: "lowLevel",
          query: {
            zoneId: Route.query.zoneId,
            zoneName: Route.query.zoneName
          }
        });
        return;
      }
      Router.push({
        name: "policyList",
        query: {
          activeNum: "6",
        },
      });
    };

    const backLast = () => {
      Router.back();
      eventBus.emit("getVisionList");
    };

    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };

    const previewFile = (val) => {
      let iamgesList = ["png", "jpg", "jpeg", "bmp", "gif", "webp", "svg"];
      if (iamgesList.includes(val.name.split(".")[1])) {
        let a = document.createElement("a");
        a.setAttribute("href", val.url);
        a.setAttribute("target", "_blank");
        a.setAttribute("download", val.name);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      if (val.name.split(".")[1] == "pdf") {
        let newHref = val.url;
        if (val.url.includes(windowOrigin)) {
          newHref = "/portal" + val.url.split(windowOrigin)[1];
        }
        let previewUrl = Router.resolve({
          name: "lookPdf",
          query: {
            urlMsg: encodeURIComponent(
              windowOrigin + newHref + "?token=" + token
            ),
            urlName: val.name,
          },
        });
        window.open(previewUrl.href, "_blank");
        return;
      }
      data.viewLoading = true;
      pptTopdf({
        filePath: val.path,
        fileUrl: val.url,
      }).then((res) => {
        data.viewLoading = false;
        if (res.code == 200) {
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: val.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
    };

    const proDetail = (val) => {
      Router.push({
        query: {
          id: val.id,
        },
        name: "solveDetailNew",
      });
    };

    const proSceneDetail = (val) => {
      Router.push({
        query: {
          id: val.id,
          activeBtn: 2,
        },
        name: "applyNew",
      });
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };

    const contentSceneColor = (index) => {
      data.cardSceneActive = index;
    };

    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const contentSceneLeave = () => {
      data.cardSceneActive = "-1";
    };

    const closeModal = () => {
      data.previewVisible = false;
      data.ticketShow = false;
    };

    const addTicket = () => {
      window.location.replace(
        window.location.origin +
          "/backend/#/ticket/addTicket?routeName=waitWork&policyId=" +
          Route.query.id,
        "_blank"
      );
    };

    const ticketDetail = () => {
      data.previewVisible = false;
    };

    const cancelTicket = () => {
      data.previewVisible = false;
      data.ticketShow = false;
    };

    return {
      ...toRefs(data),
      backLast,
      back,
      Route,
      addTicket,
      closeModal,
      ticketDetail,
      cancelTicket,
      contentSceneColor,
      proDetail,
      proSceneDetail,
      paginatedData,
      paginatedSceneData,
      backgroundStyles,
      previewFile,
      contentColor,
      contentLeave,
      contentSceneLeave,
    };
  },
});
</script>

<style lang="scss" scoped src="./vision.scss"></style>

<style lang="scss">
.htmlContentImg img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}
.ticketView {
  .ant-modal-body {
    max-height: 85vh;
    overflow-y: auto;
  }
}
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>