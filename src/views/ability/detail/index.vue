<template>
  <div class="box">
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">能力汇聚</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.abilityName }}</span>
      </div>
      <div class="right_nav">
        <span @click="backBtn">返回</span>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner">
        <div class="top_card">
          <div class="left1">
            <div class="left_tit">
              <p>{{ detailData.abilityName }}</p>
              <a-tag color="processing" class="tag"
                >{{ detailData.abilityAreas }}
              </a-tag>
              <img
                src="../../../assets/images/ability/adlityDetail/watch.png"
                alt=""
              /><span>{{ detailData.viewsNum }}</span>
            </div>
            <div class="left_middle">
              <p class="info">
                {{ detailData.abilityDesc }}
              </p>
              <div class="top_userInfo flex">
                <span class="first" style="width: 180px"
                  >联系人：{{ detailData.teamContact }}</span
                >
                <span style="width: 180px"
                  >电话：{{ detailData.teamPhone }}</span
                >
                <span>邮箱：{{ detailData.email }}</span>
              </div>
              <div class="flex top_userInfo" style="margin-top: 16px">
                <span style="width: 180px"
                  >提供方：{{ detailData.provider }}</span
                >
                <span style="width: 180px"
                  >能力板块：{{ detailData.abilityAreas }}</span
                >
                <div style="margin-right: 16px">
                  交付方式：{{ detailData.deliveryMethod }}
                </div>
              </div>
            </div>
            <div class="left_bottom">
              <a-button
                type="primary"
                v-if="detailData.auditStatus == 3"
                @click="importModal"
                >我要引入</a-button
              >
            </div>
          </div>
        </div>
      </div>
      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          @click="handleClick"
          :getContainer="getContainer"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>

      <div id="anchorContent" class="content">
        <div class="tab_content" id="#function">
          <div class="tit">应用场景</div>
        </div>
        <div class="card applyCard">
          <div class="card_content" style="padding-top: 0px; display: block">
            <a-tabs v-model:activeKey="activeKey" class="apply">
              <a-tab-pane
                :tab="item.name"
                v-for="(item, key) in detailData.sceneList"
                :key="key + 1"
              >
                <img :src="`${item.cover}`" alt="" class="img" />
                <div class="right">
                  <p class="desc" style="height: 120px; line-height: 28px">
                    {{ item.content }}
                  </p>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
        <div class="tab_content" id="#advantage">
          <div class="tit">项目案例</div>
        </div>
        <div class="card applyCard">
          <div class="card_content" style="padding-top: 0px; display: block">
            <a-tabs v-model:activeKey="ApplyKey" class="apply">
              <a-tab-pane
                :tab="item.name"
                v-for="(item, key) in detailData.caseList"
                :key="key + 1"
              >
                <img :src="`${item.cover}`" alt="" class="img" />
                <div class="right">
                  <p class="desc" style="height: 120px; line-height: 28px">
                    {{ item.content }}
                  </p>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
        <div
          class="download"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
        >
          <div class="tit">文档及资费</div>
          <ul class="list">
            <li v-for="(item, key) in detailData.fileList">
              <div class="li_box" @click="fileShow(item)">
                <div class="left_box">
                  <img
                    src="../../../assets/images/ability/adlityDetail/word.png"
                    alt=""
                    @click="doladFile"
                    style="width: 40px; height: 40px"
                  />
                  <p>{{ item.name }}</p>
                </div>
                <img
                  src="../../../assets/images/ability/adlityDetail/download.png"
                  alt=""
                  @click.stop="downloadBtn(item)"
                  style="cursor: pointer"
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <a-modal
      :visible="previewVisible"
      title="引入申请"
      @cancel="closeModal"
      :footer="null"
      width="600px"
    >
      <a-form :model="formState" id="form" :rules="rules" ref="formRef">
        <a-form-item label="所属组织" name="companyId">
          <a-select
            v-model:value="formState.companyId"
            placeholder="请选择所属组织"
          >
            <a-select-option
              v-for="item in companyList1"
              :key="item.id"
              :value="item.id"
              >{{ item.companyName }}</a-select-option
            >
          </a-select>
        </a-form-item>
        <a-form-item label="应用项目名称" name="projectName">
          <a-input
            v-model:value="formState.projectName"
            placeholder="请输入应用项目名称"
          />
        </a-form-item>
        <a-form-item label="项目签约金额（元）" name="contractAmount">
          <a-input
            v-model:value="formState.contractAmount"
            placeholder="请输入项目签约金额（元）"
          />
        </a-form-item>
        <a-form-item label="联系人" name="contacts">
          <a-input
            v-model:value="formState.contacts"
            placeholder="请输入联系人"
          />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item label="电话" name="telephone">
          <a-input
            v-model:value="formState.telephone"
            placeholder="请输入电话"
          />
        </a-form-item>
        <a-form-item style="text-align: center">
          <a-button style="margin-right: 20px" @click="closeModal"
            >取消</a-button
          >
          <a-button type="primary" @click="onSubmit">我要申请</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      :visible="showFile"
      title="资料附件下载"
      @cancel="handleClose"
      class="fileData"
      width="80%"
      :footer="null"
    >
      <div class="content">
        <checkModel :data="detailData.fileList" />
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { reactive, onMounted, ref, UnwrapRef, toRaw, watch } from "vue";
import { RouterView, useRouter, useRoute } from "vue-router";
import { Anchor } from "ant-design-vue";
import { getDetail, importProject } from "../../../api/ability/detail";
import { getBuyList } from "../../../api/info/index";
import { getCompanyList } from "../../../api/ability/home";
import { getMakeUrl } from "../../../utils/getUrl";
import { message } from "ant-design-vue";
import { ValidateErrorEntity } from "ant-design-vue/es/form/interface";
import checkModel from "./check.vue";
import axios from "axios";
interface FormState {
  projectName: string;
  contractAmount: string;
  contacts: string;
  email: string;
  telephone: string;
  companyId: string | undefined;
}
const baseURL = getMakeUrl();

const targetOffset = ref<number | undefined>(undefined);
const route = useRoute();
onMounted(() => {
  getData();
});
// 弹框
const fileIdList = ref([]);
const handleClose = (e: MouseEvent) => {
  showFile.value = false;
};
// 弹框结束

const activeKey = ref(1);
const formRef = ref();
const formState: UnwrapRef<FormState> = reactive({
  projectName: "",
  contacts: "",
  telephone: "",
  contractAmount: "",
  email: "",
  companyId: undefined,
  abilityId: route.query.id,
});
const rules = {
  projectName: [
    { required: true, message: "请输入应用项目名称", trigger: "blur" },
  ],
  companyId: [{ required: true, message: "请选中所属组织", trigger: "blur" }],
  contacts: [{ required: true, message: "请输入联系人", trigger: "blur" }],
  telephone: [
    { required: true, message: "请输入联系方式", tirgger: "blur" },
    { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: "请正确输入手机号码" },
  ],
  contractAmount: [
    { required: true, message: "请输入项目签约金额", tigger: "blur" },
  ],
  email: [
    { required: true, message: "请输入邮箱", tigger: "blur" },
    {
      pattern:
        /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/,
      message: "请正确输入邮箱",
    },
  ],
};
const ApplyKey = ref(1);
const Rules = ref([]);
const downloadId = ref("");
const docxFile = ref("");
const previewVisible = ref(false);
const open = ref<boolean>(false);
const fileOpen = ref<boolean>(false);
// 下载文件
const downloadBtn = (e) => {
  downloadId.value = e.path;
  const href = baseURL + e.path;
  window.open(href)
//const downName = e.name;
//axios
//  .get(href, { responseType: "blob" })
//  .then((res) => {
//    const blob = new Blob([res.data]);
//    const link = document.createElement("a");
//    link.href = URL.createObjectURL(blob);
//    link.download = downName;
//    link.click();
//    URL.revokeObjectURL(link.href);
//  })
//  .catch(console.error);
};
const fileShow = (val) => {
  let typeFile = val.path.split(".").pop();
  if (typeFile == "ppt" || typeFile == "pptx" || typeFile == "xmind") {
    message.warning("该文件类型暂不支持在线预览");
    return;
  }
  let previewUrl = Router.resolve({
    name: "viewFile",
    query: {
      docxFile: `${baseURL}${val.path}`,
    },
  });
  window.open(previewUrl.href, "_blank");
};
const userStatus = ref(true);

const currentAnchor = ref("#function");
const scrollStep = ref(100);
const back = () => {
  Router.push({
    name: "thirdView",
  });
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#function";
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

const importModal = () => {
  previewVisible.value = true;
};
const closeModal = () => {
  previewVisible.value = false;
  formRef.value.resetFields();
  formState.companyId = undefined;
};
const showFile = ref<boolean>(false);
const backBtn = () => {
  history.go(-1)
};
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      importProject(toRaw(formState)).then((res) => {
        formRef.value.resetFields();
        previewVisible.value = false;
        message.success("申请成功");
        formState.companyId = undefined;
      });
      // setTimeout(() => {
      //   showFile.value = true;
      // }, 100);
    })
    .catch((err) => {
      console.log("error", err);
    });
};
const companyList1 = ref({});
const detailData = ref({});
const getData = () => {
  getDetail(route.query.id)
    .then((res) => {
      let date = new Date(res.data.createTime);
      var Y = date.getFullYear();
      var M =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      var D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let GMT = Y + "." + M + "." + D;
      res.data.createTime = GMT;
      detailData.value = res.data;
      if (res.data.fileList.length == 0) {
        res.data.fileList = false;
        detailData.value = res.data;
      } else {
        // anchorList.value = anchorList.value.slice(0, -1);
        anchorList.value.push({
          key: "download",
          href: "#download",
          title: "文档及资费",
        });
        detailData.value = res.data;
      }
    })
    .catch((err) => {
      console.log(err);
    });
  getCompanyList().then((res) => {
    companyList1.value = res.data;
    formState.email = userInfo.mail;
    formState.contacts = userInfo.realName;
    formState.telephone = userInfo.phone;
  });
  // getBuyList()
};
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
const isActive = ref(0);
function change(v) {
  isActive.value = v;
}

const getContainer = () => {
  return document.getElementById("#anchorContent");
};

const anchorList = ref([
  {
    key: "function",
    href: "#function",
    title: "应用场景",
  },
  {
    key: "advantage",
    href: "#advantage",
    title: "项目案例",
  },
]);

const Router = useRouter();
const { Link } = Anchor;
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");

  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);

  if (document.getElementsByClassName("ant-anchor-link-active").length >= 0) {
    let btn = document.querySelector(".ant-anchor-link");
    btn.classList.toggle("ant-anchor-link-active");
  }
  srcolls &&
    srcolls.scrollIntoView({
      block: "start",
      behavior: "smooth",
    });
};
</script>
<style lang="scss" scoped>
ul,
li {
    list-style: none;
}
@import "./index.scss";
</style>