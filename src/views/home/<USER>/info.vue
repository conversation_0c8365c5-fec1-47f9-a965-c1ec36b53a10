<template>
  <div id="carousel">
    <div class="titleTop">
      <img src="@/assets/images/home/<USER>" height="117" />
    </div>
  </div>
  <div class="tabList">
    <div class="top">
      <div class="my_work workList">
        <div class="header">
          <img
            class="label"
            src="../../../assets/images/home/<USER>"
            alt=""
          />
          <img
            class="tit"
            src="../../../assets/images/home/<USER>"
            alt=""
          />
        </div>
        <div class="content commonTables">
          <a-tabs
            v-model:activeKey="activeKey"
            :tabBarStyle="{ borderBottom: 'unset' }"
            @change="workTabClick"
          >
            <a-tab-pane key="1" tab="待办工单">
              <a-table
                :columns="workingColums"
                :data-source="workingData"
                :scroll="{ y: 240, x: 560 }"
              >
              <template #title="{ record, text }">
                  <span @click="backanded(record, 'wait')">
                    <span style="color: #1890ff; cursor: pointer">{{
                      text
                    }}</span>
                  </span>
                </template>
                <template #source="{ text }">
                  <span v-if="text">{{ text }}</span>
                  <span v-else>-</span>
                </template>
                <template #type="{ text }">
                  {{ typeChange(text) }}
                </template>
                <template #createTime="{ text }">
                  {{ text.slice(0, 10) }}
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="2" tab="已办工单">
              <a-table
                :columns="workingColums"
                :data-source="workingData"
                :scroll="{ y: 240, x: 560 }"
              >
                <template #title="{ record, text }">
                  <span @click="backanded(record, 'finish')">
                    <span style="color: #1890ff; cursor: pointer">{{
                      text
                    }}</span>
                  </span>
                </template>
                <template #handlingTime="{ text }">
                  <span>{{ text.slice(0, 10) }}</span>
                </template>
                <template #source="{ text }">
                  <span v-if="text">{{ text }}</span>
                  <span v-else>-</span>
                </template>
                <template #type="{ text }">
                  {{ typeChange(text) }}
                </template>
                <template #auditResult="{ text }">
                  <a-tag class="tag notPublish font_14" v-if="text == 0">
                    未通过
                  </a-tag>
                  <a-tag class="tag isPublish font_14" v-if="text == 1">
                    已通过
                  </a-tag>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
          <div class="line"></div>
        </div>
      </div>
      <div class="my_work notice">
        <div class="content commonTables">
          <a-tabs
            v-model:activeKey="secondKey"
            :tabBarStyle="{ borderBottom: 'unset' }"
          >
            <a-tab-pane key="1" tab="通知公告">
              <div
                class="flex just-center"
                style="margin-top: 26px"
                v-if="noticeList.length == 0"
              >
                <span style="color: #7a94b2">暂无数据</span>
              </div>
              <ul
                v-if="noticeList.length != 0"
                class="ulList centerList"
                style="height: 230px"
              >
                <li
                  v-for="(item, key) in noticeList"
                  :key="key"
                  @click="getDetail(item.noticeId)"
                  style="cursor: pointer"
                  class="hoverLi"
                >
                  <span class="left">
                    <span class="con hoverLi" v-if="item.title.length >= 16"
                      >{{ item.title.slice(0, 16) }}...</span
                    >
                    <span class="con hoverLi" v-else>{{ item.title }}</span>
                  </span>
                  <span class="time">{{ item.updateTime.slice(0, 10) }}</span>
                </li>
              </ul>
            </a-tab-pane>
            <a-tab-pane key="2" tab="动态中心">
              <div
                class="flex just-center"
                style="margin-top: 26px"
                v-if="centerList.length == 0"
              >
                <span style="color: #7a94b2">暂无数据</span>
              </div>
              <ul
                v-if="centerList.length != 0"
                class="ulList centerList"
                style="height: 230px"
              >
                <li
                  v-for="(item, key) in centerList"
                  :key="key"
                  @click="getActiveDetail(item.id)"
                  style="cursor: pointer"
                  class="hoverLi"
                >
                  <span class="left">
                    <span class="con hoverLi" v-if="item.title.length >= 16"
                      >{{ item.title.slice(0, 16) }}...</span
                    >
                    <span class="con hoverLi" v-else>{{ item.title }}</span>
                  </span>
                  <span class="time">{{ item.updateTime.slice(0, 10) }}</span>
                </li>
              </ul>
            </a-tab-pane>
            <template #tabBarExtraContent>
              <span
                v-if="centerList.length != 0"
                class="more"
                @click="loginMore"
                >查看更多>></span
              >
            </template>
          </a-tabs>
        </div>
        <div class="line"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, defineComponent, reactive, toRefs, nextTick } from "vue";
import { backendUrl } from "@/utils/getUrl.js";
import { getWorkList } from "@/api/portal/home.js";
import { useRouter } from "vue-router";
import { useHomeStore } from "@/store";
import solveBac from "@/assets/images/home/<USER>";
import projectBac from "@/assets/images/home/<USER>";
import abiltyBac from "@/assets/images/home/<USER>";
import { getNoticeList } from "../../../api/IfLogin/login";
import { getList } from "../../../api/IfLogin/unLogin";
import { currentTab } from "@/store";
export default defineComponent({
  components: {
    solveBac,
    projectBac,
    abiltyBac,
  },
  setup() {
    const manageUrl = backendUrl();
    const getCurTab = currentTab();
    const baseURL = "https://ictsolution.jsisi.cn/xhly/resource/";
    const Router = useRouter();
    const data = reactive({
      workingColums: [],
      columns: [
        {
          title: "工单编码",
          key: "id",
          dataIndex: "id",
          width: 80,
        },
        {
          title: "工单标题",
          key: "title",
          dataIndex: "title",
          width: 150,
          slots:{customRender:'title'}
        },
        {
          title: "工单类型",
          key: "type",
          dataIndex: "type",
          width: 100,
          slots: { customRender: "type" },
        },
//      {
//        title: "工单来源",
//        key: "source",
//        dataIndex: "source",
//        width: 100,
//        slots: { customRender: "source" },
//      },
        {
          title: "提交人员",
          key: "realName",
          dataIndex: "realName",
          width: 100,
        },
        {
          title: "提交时间",
          key: "createTime",
          dataIndex: "createTime",
          width: 100,
          slots: { customRender: "createTime" },
        },
      ],
      columnsed: [
        {
          title: "工单编码",
          key: "id",
          dataIndex: "id",
          width: 80,
        },
        {
          title: "工单标题",
          key: "title",
          dataIndex: "title",
          width: 150,
          slots: { customRender: "title" },
        },
        {
          title: "工单类型",
          key: "type",
          dataIndex: "type",
          width: 100,
          slots: { customRender: "type" },
        },
//      {
//        title: "工单来源",
//        key: "source",
//        dataIndex: "source",
//        width: 100,
//        slots: { customRender: "source" },
//      },
        {
          title: "提交人员",
          key: "realName",
          dataIndex: "realName",
          width: 100,
        },
        {
          title: "审核结果",
          key: "auditResult",
          dataIndex: "auditResult",
          width: 100,
          slots: { customRender: "auditResult" },
        },
        {
          title: "提交时间",
          key: "handlingTime",
          dataIndex: "handlingTime",
          width: 100,
          slots: { customRender: "handlingTime" },
        },
      ],
      ablityData: [],
      workingData: [],
      noticeList: [],
      centerList: [],
      current: 0,
      clickBtn: "1",
      activeKey: "1",
      secondKey: "1",
      solveList: [],
      currentBackground: solveBac,
      isShow: false,
      workParams: {
        status: 0,
        pageNo: 1,
        pageSize: 5,
      },
      role: "",
      abilityParams: {
        type: 1,
      },
    });
    const preloadImages = () => {
      [solveBac, projectBac, abiltyBac].forEach((src) => {
        const img = new Image();
        img.src = src;
      });
    };
    const typeChange = (type) => {
      let typeStr = {
        1: "上架审核",
        0: "下架审核",
        2: "撰写审核"
      };
      return typeStr[type];
    };
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    if (userInfo.roleId == 1) data.workingColums = data.columns;
    if (userInfo.roleId == 1) data.role = "1";
    if (userInfo.roleId == 2) data.role = "";
    if (userInfo.roleId == 2) data.workingColums = data.columns;
    const counterStore = useHomeStore();
    const userStatus = () => {
      if (data.isShow == false) {
        let params = {
          pageNo: 1,
          pageSize: 6,
          status: 2,
        };
        getList(params)
          .then((res) => {
            data.centerList = res.data.rows;
          })
          .catch((error) => {});
      }
      let noticeParams = {
        pageNo: 1,
        pageSize: 6,
      };
      getNoticeList(noticeParams)
        .then((res) => {
          data.noticeList = res.data.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    };
    const backanded = (record, type) => {
      type == "wait"
        ? window.open(window.location.origin + "/#/dispatchCenter/workbench?tab=waitWork", "_balnk")
        : window.open(window.location.origin + "/#/dispatchCenter/workbench?tab=finishWork", "_balnk");
    };
    const router = useRouter();
    onMounted(() => {
      preloadImages();
      userStatus();
    });
    const getDetail = (e) => {
      router.push({
        query: {
          id: e,
          type: 1,
        },
        name: "detail",
      });
    };
    const getActiveDetail = (e) => {
      router.push({
        query: {
          id: e,
          type: 2,
        },
        name: "detail",
      });
    };
    const getUnloginActiveDetail = (e) => {
      router.push({
        query: {
          id: e,
          type: 2,
          ifLogin: 9,
        },
        name: "detail",
      });
    };
    const workTabClick = () => {
      data.workingColums = data.activeKey == 1 ? data.columns : data.columnsed;
      data.workParams.status = data.activeKey == 1 ? 0 : 1;
      getWorkList(data.workParams).then((res) => {
        data.workingData = res.data.rows;

        data.workingData.forEach(function (element) {
          for (let key in element) {
            if (element[key] == null) {
              element[key] = "-";
            }
          }
        });
      });
    };
    workTabClick();
    const checkMore = () => {
      // Router.push({
      //   name: "thirdView",
      //   query: {
      //     linkUrl:
      //       "http://36.140.134.194:30092/jumplogin?jiangsuToken=" +
      //       token +
      //       "&url=/",
      //   },
      // });
      window.open(
        "http://36.140.134.194:30092/jumplogin?jiangsuToken=" +
          token +
          "&url=/",
        "_blank"
      );
    };
    const loginMore = () => {
      Router.push({
        query: {
          active: data.secondKey,
        },
        name: "isLogin",
      });
    };

    const toDetail = (val) => {
      if (data.clickBtn == 1) {
        router.push({
          query: {
            id: val,
          },
          name: "abilityDetail",
        });
      }
      if (data.clickBtn == 2) {
        let encodedParam = encodeURIComponent(val);
        window.open(
          manageUrl + "/#/ability/mySubscribe?params=" + encodedParam,
          "_balnk"
        );
      }
    };

    const toOfficeCenter = () => {
    	router.push("/officeCenter");
    }

    return {
      ...toRefs(data),
      baseURL,
      manageUrl,
      Router,
      checkMore,
      toDetail,
      userStatus,
      counterStore,
      loginMore,
      router,
      getDetail,
      getActiveDetail,
      getUnloginActiveDetail,
      workTabClick,
      backanded,
      typeChange,
      toOfficeCenter
    };
  },
});
</script>
<style lang="scss" scoped>
@import "./tanle.scss";
</style>
<style lang="scss" scoped>
ul,
li {
    list-style: none;
}
.more {
  cursor: pointer;
}
#carousel {
  transition: background-image 1s ease-in-out;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 440px;
  background-image: url("@/assets/images/home/<USER>");

  .titleTop {
    margin-left: 125px;
    padding-top: 141px;
  }
}

.commonTable {
  :deep(.ant-tabs-top-content) {
    height: 305px;
  }
}

.commonTables {
  :deep(.ant-tabs-top-content) {
    height: 285px;
  }
}

.word_des {
  margin: 0 120px;
  top: 72px;
  position: relative;
  height: 148px;

  .category {
    font-size: 16px;
    color: rgba(36, 40, 49, 0.45);
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    right: 119px;
    top: 56px;

    .common {
      margin-top: 11px;
    }

    .activeTab {
      color: #325a88;
    }
  }

  .project {
    font-weight: bold;
    font-size: 42px;
    color: #24456a;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 32px;
  }

  .text {
    font-weight: 500;
    font-size: 80px;
    color: #acc4e8;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}
.carouselClass {
  margin: 0 120px;
  margin-top: 40px;
}
.information {
  display: flex !important;
  // gap: 0 120px;
  height: 240px;
  margin-top: 30px;
  justify-content: space-around;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
  border-radius: 10px;

  .conclusion {
    display: flex;
    flex-direction: column;
    align-items: center;

    .number {
      font-family: DIN, DIN;
      font-weight: bold;
      font-size: 36px;
      color: #236cff;

      .num_text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .total {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
      margin-top: 10px;
    }

    .support {
      font-size: 14px;
      margin-top: 11px;
      color: rgba(0, 0, 0, 0.45);
    }
    .rateText {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

:deep(.slick-slide) {
  height: 350px;
  overflow: hidden;
}

:deep(li > button) {
  width: 24px !important;
  height: 6px !important;
  background: #ccd8eb !important;
  border-radius: 8px !important;
}

:deep(li.slick-active > button) {
  height: 6px !important;
  background: #325a88 !important;
  border-radius: 8px !important;
}

:deep(.slick-dots > li) {
  margin-right: 23px !important;
  background: #ccd8eb;
  border-radius: 8px;
}

.serviceContent {
  margin-top: 0px;
  overflow: hidden;
  padding: 0 120px 0 120px;
  height: 900px;
  background-image: url("@/assets/images/home/<USER>");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  .moveSite {
    margin-top: 55px;
    text-align: center;
    font-weight: bolder;

    p {
      margin-bottom: 0;
    }

    .service {
      font-size: 76px;
      color: #e1e9f5;
    }

    .hotArrow {
      font-size: 46px;
      color: #24456a;
    }

    .capacity {
      margin-top: -96px;
      font-size: 46px;
      color: #24456a;
    }
  }

  .bluePhoto {
    left: 120px;
    width: 143px;
    height: 328px;
    color: rgba(0, 0, 0, 0.85);
    background: linear-gradient(
      208deg,
      #91b5fe 0%,
      rgba(192, 213, 255, 0.2) 100%
    );
    position: absolute;
  }
}

.rightContent {
  margin-left: 30px;
  position: relative;

  .solution {
    position: relative;
    justify-content: space-between;

    .arrow-right {
      position: absolute;
      opacity: 0.85;
      left: 150px;
      top: 5px;

      .arrow-line {
        display: inline-block;
        width: 112px;
        height: 2px;
        background-color: #24456a;
        margin-left: -12px;
      }

      .arrow {
        width: 10px;
        height: 10px;
        border-top: 2px solid #325a88;
        border-right: 2px solid #325a88;
        transform: rotate(45deg);
        position: absolute;
        left: 91px;
        top: 10px;
      }
    }
  }

  .right_title {
    font-weight: 500;
    font-size: 28px;
    color: #24456a;
  }

  .right_text {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 400;
    line-height: 34px;
    margin-top: 16px;
  }

  .checkMore {
    width: 90px;
    height: 28px;
    background: rgba(30, 99, 255, 0.06);
    border-radius: 4px;
    font-size: 14px;
    color: #1e63ff;
    text-align: center;
    line-height: 28px;
    float: right;
    margin: 10px 0 0 0;
  }
}

.cardPlay {
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 100px auto;
  height: 400px;

  .card_item {
    width: 48%;
    padding: 24px;
    background: #333;
    height: 160px;
    background: #f5f7fc;
    box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);

    .left {
      width: 100%;
      padding-left: 24px;
      .tag {
        border: none;
        background: #d7e6ff;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #2e7fff;
      }

      // .city {
      //   font-size: 12px;
      //   font-weight: 400;
      //   color: rgba(0, 0, 0, 0.45);
      //   display: inline-block;
      //   float: right;
      // }

      .desc {
        margin-top: 12px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .footer {
        justify-content: space-between;

        div {
          width: 50%;
        }

        .footer_right {
          text-align: right;
        }
      }
    }
  }
}
.hoverLi:hover {
  .con {
    color: #236cff !important;
  }
  .time {
    color: #236cff !important;
  }
  .tit {
    color: #236cff !important;
  }
}
// :deep(.ant-table-thead) {
//   tr {
//     th {
//       text-align: left;
//     }
//   }
// }

// :deep(.ant-table-tbody) {
//   tr {
//     td {
//       text-align: left;
//     }
//   }
// }
.tag {
  border-radius: 4px;
  border: none;
  padding: 2px 8px;
}

.isPublish {
  background: rgba(0, 189, 98, 0.1);
  color: #00bd62;
}

.notPublish {
  background: rgba(245, 29, 15, 0.1);
  color: #f51d0f;
}
</style>