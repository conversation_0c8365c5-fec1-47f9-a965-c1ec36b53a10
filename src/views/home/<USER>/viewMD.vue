<template>
  <div style="min-height: calc(100vh - 80px); background-color: #ffffff">
    <div class="newToView">
      <a-radio-group
        v-model:value="viewType"
        button-style="solid"
        @change="toView"
      >
        <a-radio-button value="1">操作手册</a-radio-button>
        <a-radio-button value="2">FAQ</a-radio-button>
        <a-radio-button value="3">培训视频</a-radio-button>
        <a-radio-button value="4">平台彩页</a-radio-button>
      </a-radio-group>
    </div>
    <div class="null" v-if="noData">
      <div style="height: 100%" v-if="viewType == '3'">
        <div class="title">培训视频</div>
        <div class="line"></div>
        <div class="body">
          <video id="video" ref="videoRef" controls :src="videoUrl" />
        </div>
      </div>
      <div style="height: 100%" v-if="viewType == '4'">
        <div class="title">平台彩页</div>
        <div class="line"></div>
        <div class="body">
          <!--<video id="video" ref="videoRef" controls :src="file.url" />-->
          <img
            class="colorPage"
            src="../../../assets/images/home/<USER>"
            alt=""
          />
        </div>
      </div>
    </div>
    <div v-else class="container">
      <!-- 左侧导航栏 -->
      <div class="anchor-nav" v-if="treeData.length">
        <!-- <h1 style="font-weight: 700;margin-bottom: 0;">{{ treeData[0].title }}</h1> -->
        <div
          v-for="item in treeData"
          :key="item.lineIndex"
          :class="{ active: activeLine === item.lineIndex }"
          :style="{ paddingLeft: `${item.indent * 20}px` }"
        >
          <h1
            style="font-weight: 700; margin-bottom: 5px"
            @click="scrollToAnchor(item.id)"
          >
            {{ item.title }}
          </h1>
          <div v-for="val in item.children" style="margin-left: 20px">
            <h2
              style="font-weight: 600; margin-bottom: 5px"
              @click.stop="scrollToAnchor(val.id)"
              v-if="val.level == 2"
            >
              {{ val.title }}
            </h2>
            <div v-for="value in val.children" style="margin-left: 30px">
              <h3
                style="font-weight: 700; margin-bottom: 5px"
                v-if="value.level == 3"
                @click.stop="scrollToAnchor(value.id)"
              >
                {{ value.title }}
              </h3>
            </div>
          </div>
        </div>
      </div>

      <!-- Markdown 预览区 -->
      <div class="preview-area">
        <v-md-preview
          :text="mdContent"
          @change="handleTitles"
          height="calc(100vh - 60px)"
        />
      </div>
      <div class="fixed-download-btn" @click="handleDownload">
        <img class="img_bot" src="@/assets/images/down.png" alt="" />
        <p class="p_bot">{{ type }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ref,
  toRefs,
  watch,
  defineComponent,
  reactive,
  nextTick,
  onMounted,
  onUnmounted,
} from "vue";
import axios from "axios";
import { debounce } from "lodash-es";
import { useRouter, useRoute } from "vue-router";
import { getHelp } from "@/api/system/user.js";
import markdownIt from "markdown-it";
import markdownItAttrs from "markdown-it-attrs";
export default defineComponent({
  setup() {
    const data = reactive({
      videoUrl:
        window.location.origin +
        "/portal/template/麒麟平台讲解培训视频514.mp4?token=" +
        localStorage.getItem("token"),
      viewType: "1",
      text: "",
      url: "",
      name: "",
      noData: true,
      type: "",
    });
    const md = markdownIt().use(markdownItAttrs);
    const Router = useRouter();
    const Route = useRoute();
    const loadMarkdown = async (url) => {
      try {
        const response = await axios.get(url, {
          responseType: "arraybuffer",
          transformResponse: [
            (data) => {
              // 尝试 UTF-8 解码
              const utf8Content = new TextDecoder("utf-8").decode(data);
              // 检查是否有乱码
              if (!utf8Content.includes("�")) {
                return utf8Content;
              }
              // 尝试 GBK 解码
              return new TextDecoder("gbk").decode(data);
            },
          ],
        });

        mdContent.value = response.data;
        const parser = new DOMParser();
        const doc = parser.parseFromString(mdContent.value, "text/html");
        await nextTick();
        const headings = doc.querySelectorAll("h1, h2, h3, h4, h5");
        headings.forEach((heading, index) => {
          heading.setAttribute("id", index + 1);
        });
        const serializer = new XMLSerializer();
        const htmlString = serializer.serializeToString(doc);
        const bodyContent = doc.body.innerHTML;
        mdContent.value = bodyContent;
        data.noData = false;
      } catch (error) {
        console.error("MD 文件加载失败", error);
      }
    };
    const mdContent = ref("");
    const treeData = ref([]);
    const activeLine = ref(null);
    const handleTitles = (titles) => {
      // console.log(titles, "titles");
      const parser = new DOMParser();
      const doc = parser.parseFromString(titles, "text/html");
      // const headings = doc.querySelectorAll("h1, h2, h3");
      const headings = Array.from(
        doc.querySelectorAll("h1, h2, h3, h4, h5")
      ).map((heading, index) => {
        heading.setAttribute("data-line-index", index + 1);
        heading.setAttribute("id", index + 1);
        return heading;
      });
      // console.log(headings, "headings");

      const navItems = Array.from(headings).map((heading) => {
        return {
          id: heading.id,
          title: heading.textContent.trim(),
          level: parseInt(heading.tagName.replace("H", ""), 10),
          lineIndex: heading.dataset.lineIndex,
        };
      });
      treeData.value = buildTreeStructure(navItems); // 构建树形结构
      console.log(treeData.value, "treeData");
    };

    // 树形结构生成（支持多级嵌套）
    const buildTreeStructure = (items) => {
      const tree = [];
      const stack = [{ level: 0, children: tree }];
      items.forEach((item) => {
        const indent = item.level - 1;
        while (stack.length > 1 && item.level <= stack.at(-1).level) {
          stack.pop(); // 自动回溯到最近合法父级
        }
        // 挂载节点
        const parent = stack.at(-1);
        const node = { ...item, indent: item.level - 1, children: [] };
        parent.children.push(node);
        stack.push({ level: item.level, children: node.children });
      });

      return tree;
    };
    const scrollToAnchor = (lineIndex) => {
      console.log(lineIndex, "lineIndex");

      nextTick(() => {
        const target = document.getElementById(lineIndex);
        // activeLine.value = lineIndex;
        if (target) {
          target.scrollIntoView({ behavior: "auto" });
          setTimeout(() => target.scrollIntoView({ behavior: "smooth" }), 50);
        }
      });
    };

    // 初始化加载
    // onMounted(async () => {
    //   await loadMarkdown(data.url);
    // });

    // 滚动自动定位（带防抖）
    const updateActiveLine = debounce(() => {
      const headings = Array.from(
        document.querySelectorAll("h1, h2, h3")
      ).filter((h) => h.dataset.lineIndex);

      let closest = null;
      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100 && (!closest || rect.top > closest.top)) {
          closest = {
            lineIndex: heading.dataset.lineIndex,
            top: rect.top,
          };
        }
      });
      if (closest) activeLine.value = closest.lineIndex;
    }, 100);

    onMounted(() => {
      nextTick(() => {
        const container = document.querySelector(".preview-area") || window;
        container.addEventListener("scroll", updateActiveLine);
        updateActiveLine(); // 初始化触发
      });
    });

    onUnmounted(() => {
      const container = document.querySelector(".preview-area") || window;
      container.removeEventListener("scroll", updateActiveLine);
    });

    const getData = (val) => {
      getHelp({ type: val }).then((res) => {
        if (val == 0) {
          data.type = "下载指南";
        } else {
          data.type = "下载手册";
        }
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref1 = res.data.mdurl;
        if (res.data.mdurl.includes(windowOrigin)) {
          newHref1 = "/portal" + res.data.mdurl.split(windowOrigin)[1];
        }
        let newHref2 = res.data.wordurl;
        if (res.data.wordurl.includes(windowOrigin)) {
          newHref2 = "/portal" + res.data.wordurl.split(windowOrigin)[1];
        }
        loadMarkdown(windowOrigin + newHref1 + "?token=" + token);
        data.url = windowOrigin + newHref2 + "?token=" + token;
        data.name = res.data.wordname;
      });
    };
    getData(1);

    const handleDownload = () => {
      const a = document.createElement("a");
      a.href = data.url;
      a.download = data.name; // 自定义文件名
      a.click();
    };

    const toView = () => {
      console.log(data.viewType);
      if (data.viewType == 1) {
        data.noData = false;
        getData(1);
      } else if (data.viewType == 2) {
        data.noData = false;
        getData(0);
      } else if (data.viewType == 3) {
        data.noData = true;
      } else if (data.viewType == 4) {
        data.noData = true;
      }
    };

    return {
      ...toRefs(data),
      handleDownload,
      Router,
      Route,
      loadMarkdown,
      handleTitles,
      getData,
      updateActiveLine,
      scrollToAnchor,
      activeLine,
      treeData,
      mdContent,
      md,
      toView,
    };
  },
});
</script>
<style scoped>
::v-deep(.v-md-view) {
  white-space: pre-wrap !important; /* 保留换行和空格 */
  word-break: break-all !important; /* 长文本自动换行 */
}
.container {
  display: flex;
  height: 100%;
  background: #fff;
  overflow: auto;
  overflow-y: hidden;
}
.null {
  height: calc(100vh - 60px);
  background: #fff;
  .title {
    font-size: 16px;
    line-height: 30px;
    color: #000000;
    text-align: left;
    padding: 40px 0 16px 40px;
    background-color: #ffffff;
  }
  .line {
    width: calc(100% - 80px);
    height: 1px;
    background-color: #e9e9e9ff;
    margin: 0 auto;
  }
  .body {
    background-image: url(../../../assets/images/AI/newHomeBg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: calc(100% - 30px - 40px - 16px - 1px);
    overflow: auto;
    text-align: center;
    #video {
      width: 1000px;
      margin: 30px auto;
    }
    .colorPage {
      margin: 40px auto;
    }
  }
}
.anchor-nav {
  width: 340px;
  padding: 20px;
  position: fixed;
  top: 60px;
  bottom: 0;
  overflow-y: auto;
  border-right: 1px solid #eee;
  z-index: 100;
}
.anchor-nav > div {
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 2px solid transparent;
}
.anchor-nav > div:hover {
  background: #f8f8f8;
}
.anchor-nav > div.active {
  color: #1890ff;
  border-left-color: #1890ff;
  font-weight: 500;
}

.preview-area {
  flex: 1;
  margin-left: 340px;
  padding: 20px 40px;
}
.fixed-download-btn {
  position: fixed;
  cursor: pointer;
  right: 40px;
  top: 130px;
  z-index: 999;
  border-radius: 16px 16px 16px 16px;
  border: 1px solid #225cfd;
  display: flex;
  padding: 4px;
  font-weight: 500;
  font-size: 14px;
  color: #0c70eb;
  line-height: 20px;
  justify-content: center;
  align-items: center;
  background: #ffffff;
}
.img_bot {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.p_bot {
  margin-bottom: 0;
  margin-right: 12px;
}
.newToView {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  .ant-radio-group {
    border: 1px solid #1990ffff;
    border-radius: 4px;
    .ant-radio-button-wrapper {
      min-width: 100px;
      text-align: center;
      color: #666666ff;
    }
    .ant-radio-button-wrapper-checked {
      background-color: #1990ffff;
      border-color: #1990ffff;
      color: white;
    }
  }
}
</style>

  