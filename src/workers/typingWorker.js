// typingWorker.js
let queue = [];
let isProcessing = false;

function sleep(ms) {
  return new Promise(r => setTimeout(r, ms));
}

async function processQueue() {
  if (isProcessing) return;
  isProcessing = true;
  while (queue.length) {
    const { id, itemId, text, speed = 50 } = queue.shift();
    let output = '';
    for (let i = 0; i < text.length; i++) {
      output += text[i];
      if (id) {
        postMessage({ type: 'typingUpdate', id, text: output });
      } else if (itemId) {
        postMessage({ type: 'typingUpdate', itemId, text: output });
      }
      await sleep(speed);
    }
    if (id) {
      postMessage({ type: 'typingDone', id });
    } else if (itemId) {
      postMessage({ type: 'typingDone', itemId });
    }
  }
  isProcessing = false;
}

onmessage = function(e) {
  if (e.data.type === 'addToQueue') {
    queue.push(e.data.item);
    processQueue();
  }
  if (e.data.type === 'clearQueue') {
    queue = [];
  }
};
