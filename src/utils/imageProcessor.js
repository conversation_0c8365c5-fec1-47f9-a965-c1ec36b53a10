// 图片处理常量
export const MAX_IMAGE_WIDTH = 1038;
export const IMAGE_SPACING = 24;
export const LARGE_IMAGE_THRESHOLD = 800;
export const MEDIUM_IMAGE_THRESHOLD = 390;
export const SMALL_IMAGE_THRESHOLD = 200;
export const IMAGE_SCALE_FACTOR = 1.3;

/**
 * 获取图片尺寸
 * @param {string} url - 图片URL
 * @returns {Promise<{width: number, height: number}>}
 */
const getImageSize = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve({ width: img.naturalWidth, height: img.naturalHeight });
    img.onerror = reject;
    img.src = url;
  });
};

/**
 * 获取所有节点
 * @param {Element} element - DOM元素
 * @returns {Array} 节点数组
 */
const getAllNodes = (element) => {
  let nodes = [];
  for (let child of element.childNodes) {
    if (child.nodeType === Node.ELEMENT_NODE) {
      if (child.tagName === 'IMG') {
        nodes.push(child);
      } else {
        nodes.push(...getAllNodes(child));
      }
    } else {
      nodes.push(child);
    }
  }
  return nodes;
};

/**
 * 分析富文本中连续图片
 * @param {string} richText - 富文本内容
 * @returns {Promise<Array>} 连续图片组
 */
const findConsecutiveImages = async (richText) => {
  if (!richText.trim()) return [];
  try {
    const parser = new DOMParser();
    const documentHtml = parser.parseFromString(richText, 'text/html');
    // 获取所有节点
    const allNodes = getAllNodes(documentHtml.body);
    let consecutiveGroups = [];
    let currentGroup = [];
    for (let node of allNodes) {
      if (node.tagName === 'IMG') {
        currentGroup.push({
          src: node.src,
        });
      } else {
        // 遇到非图片节点，结束当前连续组
        if (currentGroup.length > 0) {
          consecutiveGroups.push([...currentGroup]);
          currentGroup = [];
        }
      }
    }
    // 处理最后一组
    if (currentGroup.length > 0) {
      consecutiveGroups.push(currentGroup);
    }
    return consecutiveGroups;
  } catch (error) {
    return [];
  }
};

/**
 * 处理单张图片
 * @param {Object} image - 图片对象
 */
const processSingleImage = (image) => {
  if (image.width >= LARGE_IMAGE_THRESHOLD) {
    // 大图：等比例缩放到1038PX
    const scale = MAX_IMAGE_WIDTH / image.width;
    image.showWidth = MAX_IMAGE_WIDTH;
    image.showHeight = Math.trunc(scale * image.height);
  } else if (image.width >= MEDIUM_IMAGE_THRESHOLD) {
    // 中图：放大1.3倍
    image.showWidth = Math.trunc(IMAGE_SCALE_FACTOR * image.width);
    image.showHeight = Math.trunc(IMAGE_SCALE_FACTOR * image.height);
  } else if (image.width >= SMALL_IMAGE_THRESHOLD) {
    // 小图：放大1.3倍
    image.showWidth = Math.trunc(IMAGE_SCALE_FACTOR * image.width);
    image.showHeight = Math.trunc(IMAGE_SCALE_FACTOR * image.height);
  } else {
    // 小于200px的图片保持原尺寸
    image.showWidth = image.width;
    image.showHeight = image.height;
  }
  // 校验宽度不超过1038
  if (image.showWidth > MAX_IMAGE_WIDTH) {
    const scale = MAX_IMAGE_WIDTH / image.showWidth;
    image.showWidth = MAX_IMAGE_WIDTH;
    image.showHeight = Math.trunc(image.showHeight * scale);
  }
};

/**
 * 处理多张图片
 * @param {Array} images - 图片数组
 * @returns {Array} 处理后的行数组
 */
const processMultipleImages = (images) => {
  const rows = [];
  let currentRow = [];
  let currentRowWidth = 0;
  for (let i = 0; i < images.length; i++) {
    const image = images[i];
    // 大图单独占一行
    if (image.width >= LARGE_IMAGE_THRESHOLD) {
      // 先处理当前行（如果有图片）
      if (currentRow.length > 0) {
        processRow(currentRow, currentRowWidth);
        rows.push([...currentRow]);
        currentRow = [];
        currentRowWidth = 0;
      }
      // 大图等比例缩放到1038
      const scale = MAX_IMAGE_WIDTH / image.width;
      image.showWidth = MAX_IMAGE_WIDTH;
      image.showHeight = Math.trunc(scale * image.height);
      rows.push([image]);
      continue;
    }
    // 中图/小图处理：先按1.3倍放大
    let showWidth, showHeight;
    if (image.width >= MEDIUM_IMAGE_THRESHOLD) {
      // 中图：放大1.3倍
      showWidth = Math.trunc(IMAGE_SCALE_FACTOR * image.width);
      showHeight = Math.trunc(IMAGE_SCALE_FACTOR * image.height);
    } else if (image.width >= SMALL_IMAGE_THRESHOLD) {
      // 小图：放大1.3倍
      showWidth = Math.trunc(IMAGE_SCALE_FACTOR * image.width);
      showHeight = Math.trunc(IMAGE_SCALE_FACTOR * image.height);
    } else {
      // 小于200px的图片保持原尺寸
      showWidth = image.width;
      showHeight = image.height;
    }
    // 检查是否能与当前行的图片并排
    const spacing = currentRow.length > 0 ? IMAGE_SPACING : 0; // 图片间距
    const totalWidth = currentRowWidth + showWidth + spacing;
    if (totalWidth <= MAX_IMAGE_WIDTH) {
      // 可以放在当前行
      image.showWidth = showWidth;
      image.showHeight = showHeight;
      currentRow.push(image);
      currentRowWidth = totalWidth;
    } else {
      // 需要换行
      if (currentRow.length > 0) {
        processRow(currentRow, currentRowWidth);
        rows.push([...currentRow]);
      }
      // 开始新行
      image.showWidth = showWidth;
      image.showHeight = showHeight;
      currentRow = [image];
      currentRowWidth = showWidth;
    }
  }
  // 处理最后一行
  if (currentRow.length > 0) {
    processRow(currentRow, currentRowWidth);
    rows.push(currentRow);
  }
  return rows;
};

/**
 * 处理每一行的图片尺寸调整
 * @param {Array} row - 行图片数组
 * @param {number} totalWidth - 总宽度
 */
const processRow = (row, totalWidth) => {
  if (row.length === 1) {
    // 单张图片的行，检查是否需要缩放
    const image = row[0];
    if (image.showWidth > MAX_IMAGE_WIDTH) {
      const scale = MAX_IMAGE_WIDTH / image.showWidth;
      image.showWidth = MAX_IMAGE_WIDTH;
      image.showHeight = Math.trunc(image.showHeight * scale);
    }
  } else if (row.length > 1) {
    // 多张图片并排处理
    // 如果总宽度超过1038，等比例缩放整行到1038
    if (totalWidth > MAX_IMAGE_WIDTH) {
      const spacing = (row.length - 1) * IMAGE_SPACING;
      const availableWidth = MAX_IMAGE_WIDTH - spacing;
      const currentContentWidth = totalWidth - spacing;
      const scale = availableWidth / currentContentWidth;
      row.forEach(image => {
        image.showWidth = Math.trunc(image.showWidth * scale);
        image.showHeight = Math.trunc(image.showHeight * scale);
      });
    }
    // 高度对齐：找到行中最小高度，统一调整所有图片到相同高度
    const minHeight = Math.min(...row.map(img => img.showHeight));
    row.forEach(image => {
      if (image.showHeight > minHeight) {
        const scale = minHeight / image.showHeight;
        image.showWidth = Math.trunc(image.showWidth * scale);
        image.showHeight = minHeight;
      }
    });
    // 最终检查：如果调整后的总宽度仍然超过1038，再次等比例缩放
    const finalTotalWidth = row.reduce((sum, img) => sum + img.showWidth, 0) + (row.length - 1) * IMAGE_SPACING;
    if (finalTotalWidth > MAX_IMAGE_WIDTH) {
      const spacing = (row.length - 1) * IMAGE_SPACING;
      const availableWidth = MAX_IMAGE_WIDTH - spacing;
      const currentContentWidth = finalTotalWidth - spacing;
      const finalScale = availableWidth / currentContentWidth;
      row.forEach(image => {
        image.showWidth = Math.trunc(image.showWidth * finalScale);
        image.showHeight = Math.trunc(image.showHeight * finalScale);
      });
    }
  }
};

// 转义CSS选择器中的特殊字符
const escapeCSSSelector = (str) => {
  return str.replace(/([$&,:;=?@#\s%<>"~`{}|^()[\]\\])/g, '\\$1');
};
/**
 * 处理HTML内容中的图片
 * @param {string} htmlContent - HTML内容
 * @returns {Promise<string>} 处理后的HTML内容
 */
export const processHtmlImages = async (htmlContent) => {
  const consecutiveGroups = await findConsecutiveImages(htmlContent);
  await Promise.all(consecutiveGroups.map(item => {
    return Promise.all(item.map(async imgItem => {
      const size = await getImageSize(imgItem.src);
      imgItem.width = size.width;
      imgItem.height = size.height;
    }));
  }));

  consecutiveGroups.forEach(group => {
    if (group.length === 1) {
      // 单张图片处理
      processSingleImage(group[0]);
    } else if (group.length > 1) {
      // 多张图片处理
      const rows = processMultipleImages(group); // rows为二维数组，表示多张图片的行数
      // 将处理后的行信息保存到 group 中
      group.rows = rows;
    }
  });

  // 更新HTML内容中的图片尺寸和布局
  if (consecutiveGroups.length > 0) {
    const parser = new DOMParser();
    const documentHtml = parser.parseFromString(htmlContent, 'text/html');

    // 先收集所有需要处理的图片元素，避免在DOM修改过程中丢失引用
    const imageElementMap = new Map();
    consecutiveGroups.forEach(group => {
      group.forEach(imgItem => {
        const img = documentHtml.querySelector(`img[src="${imgItem.src}"]`);
        if (img) {
          imageElementMap.set(imgItem.src, img);
        }
      });
    });

    // 处理每个连续图片组
    console.log('consecutiveGroups',consecutiveGroups)
    consecutiveGroups.forEach(group => {
      if (group.length > 1 && group.rows) {
        // 多张图片按行处理
        console.log('开始处理多张图片组:', group.length, '张图片，', group.rows.length, '行');
        const firstImg = imageElementMap.get(group[0].src);
        if (firstImg && firstImg.parentNode) {
          // 记录插入位置
          const parentNode = firstImg.parentNode;
          const nextSibling = firstImg.nextSibling;

          // 收集所有原始图片的引用，但先不移除
          const originalImages = [];
          group.forEach(imgItem => {
            const img = imageElementMap.get(imgItem.src);
            if (img) {
              originalImages.push(img);
            }
          });

          // 为每一行创建容器
          group.rows.forEach(row => {
            const rowContainer = documentHtml.createElement('div');
            if (row.length === 1) {
              // 单张图片的行，居中显示
              rowContainer.style.cssText = 'display: flex; justify-content: center; margin: 10px 0;';
            } else {
              // 多张图片的行，并排显示
              rowContainer.style.cssText = `display: flex; gap: ${IMAGE_SPACING}px; align-items: flex-start; margin: 10px 0; justify-content: center;`;
            }

            // 将行中的图片添加到容器
            row.forEach(imgItem => {
              const originalImg = originalImages.find(img => img.src === imgItem.src);
              const newImg = documentHtml.createElement('img');
              newImg.src = imgItem.src;
              newImg.width = imgItem.showWidth;
              newImg.height = imgItem.showHeight;
              newImg.style.cssText = 'display: block; margin: 0; cursor: pointer;';

              // 复制原始图片的其他属性
              if (originalImg) {
                if (originalImg.alt) newImg.alt = originalImg.alt;
                if (originalImg.title) newImg.title = originalImg.title;
                if (originalImg.className) newImg.className = originalImg.className;
              }

              rowContainer.appendChild(newImg);
              console.log('添加图片到行容器:', imgItem.src, `${imgItem.showWidth}x${imgItem.showHeight}`);
            });

            // 将行容器插入到文档中
            if (nextSibling) {
              parentNode.insertBefore(rowContainer, nextSibling);
            } else {
              parentNode.appendChild(rowContainer);
            }
          });

          // 最后移除所有原始图片
          originalImages.forEach(img => img.remove());
        }
      } else if (group.length === 1) {
        // 单张图片处理
        const img = imageElementMap.get(group[0].src);
        console.log('img',img)
        if (img && group[0].showWidth && group[0].showHeight) {
          img.width = group[0].showWidth;
          img.height = group[0].showHeight;
          // 移除style属性中的宽高设置
          if (img.style) {
            img.style.width = '';
            img.style.height = '';
            img.style.display = 'block';
            img.style.margin = '10px auto';
          }
        }
      }
    });
    // 返回处理后的内容
    return documentHtml.body.innerHTML;
  }

  return htmlContent;
};