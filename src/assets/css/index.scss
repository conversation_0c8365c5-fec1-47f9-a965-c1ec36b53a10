* {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

#app {
    width: 100%;
    height: 100vh;
}

html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    background: #f5f7fc;
}

.show {
    display: block;
}

.hide {
    display: none;
}

.flex {
    display: flex;
}

.flex-1 {
    flex: 1;
}

.gap-20 {
    gap: 20px;
}

.pointer {
    cursor: pointer;
}

.view {
    height: 100%;
}

.just-center {
    justify-content: center;
}

.just-end {
    justify-content: flex-end;
}

.just-start {
    justify-content: flex-start;
}

.just-sa {
    justify-content: space-around;
}

.just-sb {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.align-start {
    align-items: flex-start;
}

.align-end {
    align-items: flex-end;
}

.overflow-hidden {
    overflow: hidden;
}

.grow_1 {
    flex-grow: 1;
}

.columns {
    flex-direction: column;
}

.maxWidth {
    width: 100%;
}

.maxHeight {
    height: 100%;
}

.absolute {
    position: absolute;
}

.relative {
    position: relative;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
    width: 3px;
    height: 5px;
    background: rgba(229, 230, 231, 0) !important;
    // display: none;
    // display: flex;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset006pxrgba(221, 165, 165, 0) !important;
    background: rgba(197, 197, 197, 0) !important;
    border-radius: 0 !important;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(229, 230, 231, 1);
    -webkit-box-shadow: inset006pxrgb(0, 0, 0);
}

.tooltip_class {
    min-width: 600px
}

.ant-modal-content {
    border-radius: 8px !important;
}

.ant-modal-header {
    border-radius: 8px 8px 0 0 !important;
}
