<template>
  <div>
    <div class="mark-down">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        id="editor"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
        @onChange="getValueHtml"
      />
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import "@wangeditor/editor/dist/css/style.css";

import {
  defineComponent,
  onBeforeUnmount,
  shallowRef,
  reactive,
  toRefs,
  watch,
} from "vue";

export default defineComponent({
  name: "markDown",
  components: {
    Editor,
    Toolbar,
  },
  props: {
    valueHtml: {
      type: String,
      default() {
        return "";
      },
    },
    excludeKeys: {
      type: Array,
      default() {
        return ["group-video", "insertTable"];
      },
    },
  },
  emits: ["valueHtml-change"],
  setup(props, context) {
    const data = reactive({
      toolbarConfig: {
        // 自定义富文本工具栏
        excludeKeys: props.excludeKeys,
      },
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          fontSize: {
            fontSizeList: [
              "12px",
              "13px",
              "14px",
              "15px",
              "16px",
              "17px",
              "18px",
              "19px",
              "20px",
              "22px",
              "24px",
              "26px",
              "28px",
              "30px",
            ],
          },
          uploadImage: {
            // 不写 server，直接走自定义
            customUpload(file, insertFn) {
              const formData = new FormData();
              formData.append("file", file);
              uploadFileList(formData)
                .then((res) => {
                  if (res.code == 200) {
                    const url = res.data.fileUrl;
                    if (url) insertFn(url);
                  }
                })
                .catch(() => {});
            },
          },
        },
        // 重新配置
        hoverbarKeys: {
          text: {
            menuKeys: ["bold", "through", "color", "bgColor", "clearStyle"],
          },
        },
      },
      mode: "default",
      valueHtml: "",
      content: "",
    });

    const customParseLinkUrl = (url) => {
      if (url.indexOf("https") != 0 && url.indexOf("http") != 0) {
        return `https://${url}`;
      }
      return url;
    };

    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef();
    const handleCreated = (editor) => {
      editorRef.value = editor; // 记录 editor 实例，重要！
      let editorConfig = editor.getConfig();
      editorConfig.maxLength = 5000;
      editorConfig.MENU_CONF["insertLink"] = {
        parseLinkUrl: customParseLinkUrl,
      };
      editorConfig.MENU_CONF["editLink"] = {
        parseLinkUrl: customParseLinkUrl,
      };
    };

    const handlePStyle = (item) => {
      let obj = {};
      if (item.lineHeight) {
        obj.lineHeight = item.lineHeight;
      }
      if (item.textAlign) {
        obj.textAlign = item.textAlign;
      }
      if (item.indent) {
        obj.textIndent = item.indent;
      }
      return obj;
    };

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
      const editor = editorRef.value;
      if (editor == null) return;
      editor.destroy();
    });

    // 子组件向父组件传参，
    const getValueHtml = () => {
      const editor = editorRef.value;
      data.content = editor.children;
      context.emit("valueHtml-change", data.valueHtml, data.content);
    };

    // 初始化，回显
    const init = () => {
      data.valueHtml = props.valueHtml;
    };

    init();

    watch(
      () => props.valueHtml,
      () => {
        init();
      }
    );

    return {
      ...toRefs(data),
      editorRef,
      handleCreated,
      handlePStyle,
      getValueHtml,
    };
  },
});
</script>

<style lang="scss">
.mark-down {
  border-radius: 2px;
  z-index: 999;
}
#editor {
  height: 500px !important;
  overflow-y: auto;
}
.w-e-toolbar,
.w-e-text-container {
  background: #f5f6fa;
}
.w-e-toolbar {
  border-radius: 4px 4px 0px 0px;
  border: 1px solid #d1d0d8;
  border-bottom: none;
}
.w-e-text-container {
  background: #ffffff;
  border-radius: 0px 0px 4px 4px;
  color: rgba(0, 6, 14, 0.8);
  border: 1px solid #d1d0d8;
  border-top: 0;
}
</style>
